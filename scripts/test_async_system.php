#!/usr/bin/env php
<?php

/**
 * Async Job System Test Script
 * 
 * Validates the async job processing system configuration and functionality.
 * Use this script to test the system before deploying to production.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "🧪 ========================================\n";
echo "   ASYNC JOB SYSTEM TEST SUITE\n";
echo "========================================\n\n";

try {
    // Parse command line arguments
    $configOnly = false;
    $testCameraId = null;

    if ($argc > 1) {
        for ($i = 1; $i < $argc; $i++) {
            $arg = $argv[$i];

            if ($arg === '--config-only') {
                $configOnly = true;
                echo "🔧 Config-only mode enabled\n";
            } elseif ($arg === '--camera' && isset($argv[$i + 1])) {
                $testCameraId = (int)$argv[$i + 1];
                $i++; // Skip next argument
                echo "🎯 Testing with camera ID: {$testCameraId}\n";
            } elseif ($arg === '--help' || $arg === '-h') {
                showHelp();
                exit(0);
            }
        }
    }

    echo "\n";

    // Test 1: Load autoloader
    echo "1️⃣  Testing autoloader...\n";
    $autoloaderPath = __DIR__ . '/../vendor/autoload.php';
    if (!file_exists($autoloaderPath)) {
        throw new Exception("❌ Autoloader not found at: " . $autoloaderPath);
    }
    require $autoloaderPath;
    echo "   ✅ Autoloader loaded successfully\n\n";

    // Test 2: Load configuration
    echo "2️⃣  Testing configuration...\n";
    $configPath = __DIR__ . '/../config.php';
    if (!file_exists($configPath)) {
        throw new Exception("❌ Config file not found at: " . $configPath);
    }
    $config = require $configPath;
    echo "   ✅ Configuration loaded successfully\n";

    // Test 3: Validate async jobs configuration
    echo "3️⃣  Validating async jobs configuration...\n";
    if (!isset($config['async_jobs'])) {
        throw new Exception("❌ async_jobs configuration section missing");
    }
    
    use App\AsyncJobs\JobConfig;
    $jobConfig = new JobConfig($config);
    echo "   ✅ JobConfig initialized successfully\n";
    
    // Check screenshot configuration
    if (!$jobConfig->isJobTypeEnabled('screenshot')) {
        echo "   ⚠️  Screenshot jobs are disabled\n";
    } else {
        echo "   ✅ Screenshot jobs are enabled\n";
        
        // Validate paths
        $ffmpegPath = $jobConfig->get('async_jobs.screenshot.ffmpeg_path');
        if (!file_exists($ffmpegPath)) {
            echo "   ⚠️  FFmpeg not found at: {$ffmpegPath}\n";
        } else {
            echo "   ✅ FFmpeg found at: {$ffmpegPath}\n";
        }
        
        $outputDir = $jobConfig->get('async_jobs.screenshot.output_dir');
        if (!file_exists($outputDir)) {
            echo "   ℹ️  Output directory will be created: {$outputDir}\n";
        } else {
            echo "   ✅ Output directory exists: {$outputDir}\n";
        }
    }
    echo "\n";

    // Test 4: Database connection
    echo "4️⃣  Testing database connection...\n";
    $dbConfig = $jobConfig->getDatabaseConfig();
    $dsn = sprintf(
        "mysql:host=%s;dbname=%s;charset=%s",
        $dbConfig['host'],
        $dbConfig['dbname'],
        $dbConfig['charset']
    );

    $db = new PDO(
        $dsn,
        $dbConfig['username'],
        $dbConfig['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    echo "   ✅ Database connection successful\n";

    // Test 5: Check required tables
    echo "5️⃣  Checking database tables...\n";
    $requiredTables = ['async_jobs', 'async_job_logs', 'async_job_stats', 'async_job_workers', 'cameras'];
    foreach ($requiredTables as $table) {
        $stmt = $db->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->fetch()) {
            echo "   ✅ Table '{$table}' exists\n";
        } else {
            if ($table === 'cameras') {
                echo "   ⚠️  Table '{$table}' not found (may need to be created separately)\n";
            } else {
                echo "   ❌ Table '{$table}' not found (run migration script)\n";
            }
        }
    }
    echo "\n";

    if ($configOnly) {
        echo "🏁 Configuration test completed successfully!\n";
        echo "   The async job system configuration is valid.\n\n";
        exit(0);
    }

    // Test 6: Initialize job system
    echo "6️⃣  Initializing job system...\n";
    use App\AsyncJobs\AsyncJobManager;
    use App\AsyncJobs\JobDatabase;
    use App\AsyncJobs\JobLogger;

    $jobManager = new AsyncJobManager($config);
    echo "   ✅ AsyncJobManager initialized\n";

    $database = new JobDatabase($db, $jobConfig);
    $logger = new JobLogger($db, $jobConfig);
    echo "   ✅ Database and Logger components initialized\n\n";

    // Test 7: Check cameras
    echo "7️⃣  Checking available cameras...\n";
    $cameras = $database->getCamerasForScreenshots();
    if (empty($cameras)) {
        echo "   ⚠️  No cameras found with HLS URLs\n";
    } else {
        echo "   ✅ Found " . count($cameras) . " cameras with HLS URLs\n";
        foreach (array_slice($cameras, 0, 3) as $camera) {
            echo "      - Camera {$camera['id']}: {$camera['name']}\n";
        }
        if (count($cameras) > 3) {
            echo "      ... and " . (count($cameras) - 3) . " more\n";
        }
    }
    echo "\n";

    // Test 8: Queue status
    echo "8️⃣  Checking job queue status...\n";
    $queueStatus = $jobManager->getQueueStatus();
    foreach ($queueStatus as $jobType => $stats) {
        $status = $stats['enabled'] ? 'Enabled' : 'Disabled';
        echo "   {$jobType}: {$status}\n";
        if ($stats['enabled']) {
            echo "      Pending: {$stats['pending']}, Running: {$stats['running']}, Completed: {$stats['completed']}, Failed: {$stats['failed']}\n";
        }
    }
    echo "\n";

    // Test 9: Create test job (if camera specified)
    if ($testCameraId && !empty($cameras)) {
        echo "9️⃣  Creating test screenshot job...\n";
        
        $testCamera = array_filter($cameras, fn($c) => $c['id'] == $testCameraId);
        if (empty($testCamera)) {
            echo "   ⚠️  Camera {$testCameraId} not found or has no HLS URL\n";
        } else {
            $testCamera = reset($testCamera);
            echo "   📸 Creating job for camera: {$testCamera['name']}\n";
            
            $createdJobs = $jobManager->createScreenshotJobs([$testCameraId]);
            if (empty($createdJobs)) {
                echo "   ℹ️  No job created (may already have active job)\n";
            } else {
                echo "   ✅ Test job created: Job ID {$createdJobs[0]['job_id']}\n";
                
                // Process the test job
                echo "   🔄 Processing test job...\n";
                $results = $jobManager->processJobs('screenshot');
                
                if (!empty($results)) {
                    $result = $results[0];
                    if ($result['success']) {
                        echo "   ✅ Test job completed successfully!\n";
                        echo "      Execution time: " . round($result['execution_time'], 2) . "s\n";
                    } else {
                        echo "   ❌ Test job failed: " . $result['error'] . "\n";
                    }
                } else {
                    echo "   ℹ️  No jobs were processed\n";
                }
            }
        }
        echo "\n";
    }

    // Test 10: Cleanup
    echo "🧹 Running cleanup...\n";
    $cleanupResults = $jobManager->cleanup();
    echo "   Cleaned up {$cleanupResults['cleaned_jobs']} old jobs\n";
    echo "   Cleaned up {$cleanupResults['cleaned_logs']} old log entries\n";
    echo "   Handled {$cleanupResults['timed_out_jobs']} timed out jobs\n\n";

    echo "🎉 All tests completed successfully!\n";
    echo "   The async job system is ready for use.\n\n";

    echo "💡 Next steps:\n";
    echo "   1. Run: php scripts/capture_screenshots_async.php\n";
    echo "   2. Monitor logs and job status\n";
    echo "   3. Adjust configuration as needed\n\n";

} catch (Exception $e) {
    echo "\n❌ Test failed: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . "\n";
    echo "📍 Line: " . $e->getLine() . "\n";
    exit(1);
}

function showHelp() {
    echo "Async Job System Test Suite\n\n";
    echo "USAGE:\n";
    echo "  php test_async_system.php [options]\n\n";
    echo "OPTIONS:\n";
    echo "  --config-only       Test configuration only (no job processing)\n";
    echo "  --camera ID         Test with specific camera ID\n";
    echo "  --skip-migration    Skip database migration check\n";
    echo "  -h, --help          Show this help message\n\n";
    echo "EXAMPLES:\n";
    echo "  php test_async_system.php\n";
    echo "  php test_async_system.php --config-only\n";
    echo "  php test_async_system.php --camera 1\n\n";
}
