{"baseline_premium": {"name": "Baseline Premium (q:v 6)", "description": "Original premium baseline for comparison", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137146, "time_seconds": 0.2936, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134250, "time_seconds": 0.2255, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135879, "time_seconds": 0.21, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137195, "time_seconds": 0.2068, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136915, "time_seconds": 0.2058, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681385, "avg_time_per_frame": 0.2284, "avg_size_bytes": 136277}, "yuv444_colorspace": {"name": "YUV444 Colorspace (q:v 6)", "description": "Full chroma resolution for better gradients", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -pix_fmt yuvj444p -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 158679, "time_seconds": 0.2489, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 155539, "time_seconds": 0.2273, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 157253, "time_seconds": 0.2434, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 158653, "time_seconds": 0.2381, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 158139, "time_seconds": 0.2386, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 788263, "avg_time_per_frame": 0.2392, "avg_size_bytes": 157653}, "yuv422_optimized": {"name": "YUV422 Optimized (q:v 6)", "description": "Balanced chroma subsampling for clouds", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -pix_fmt yuvj422p -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 144209, "time_seconds": 0.2735, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 141185, "time_seconds": 0.2334, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 142856, "time_seconds": 0.2235, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 144203, "time_seconds": 0.2256, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 143876, "time_seconds": 0.2245, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 716329, "avg_time_per_frame": 0.2361, "avg_size_bytes": 143266}, "dct_algorithm": {"name": "Precise DCT Algorithm (q:v 6)", "description": "High precision DCT for better cloud details", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -dct fastint -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137110, "time_seconds": 0.3002, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134213, "time_seconds": 0.2094, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135917, "time_seconds": 0.2086, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137202, "time_seconds": 0.2001, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136897, "time_seconds": 0.1976, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681339, "avg_time_per_frame": 0.2232, "avg_size_bytes": 136268}, "high_precision_dct": {"name": "High Precision DCT (q:v 6)", "description": "Maximum precision DCT for gradients", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -dct int -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137146, "time_seconds": 0.2024, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134250, "time_seconds": 0.1992, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135879, "time_seconds": 0.1996, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137195, "time_seconds": 0.2021, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136915, "time_seconds": 0.2015, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681385, "avg_time_per_frame": 0.201, "avg_size_bytes": 136277}, "advanced_quantization": {"name": "Advanced Quantization (q:v 6)", "description": "Advanced quantization settings for clouds", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq+mv4 -quantizer_noise_shaping 2 -f mjpeg \"%s\"", "frames": {"1": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0498}, "2": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0491}, "3": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0506}, "4": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0526}, "5": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0518}}, "success_count": 0, "total_size_bytes": 0, "avg_time_per_frame": 0, "avg_size_bytes": 0}, "trellis_alternative": {"name": "Trellis Alternative (q:v 6)", "description": "Alternative optimization approach", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -threads 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137146, "time_seconds": 0.2004, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134250, "time_seconds": 0.1927, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135879, "time_seconds": 0.1969, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137195, "time_seconds": 0.199, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136915, "time_seconds": 0.1976, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681385, "avg_time_per_frame": 0.1973, "avg_size_bytes": 136277}, "slightly_better_q5": {"name": "<PERSON><PERSON><PERSON> Q5 (q:v 5)", "description": "q:v 5 with enhanced settings for comparison", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 5 -pix_fmt yuvj422p -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 146743, "time_seconds": 0.316, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 143660, "time_seconds": 0.3274, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 145227, "time_seconds": 0.318, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 146732, "time_seconds": 0.322, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 146288, "time_seconds": 0.3183, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 728650, "avg_time_per_frame": 0.3203, "avg_size_bytes": 145730}}