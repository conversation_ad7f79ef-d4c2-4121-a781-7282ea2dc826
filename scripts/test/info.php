<?php
// Проверяем, что скрипт запущен через веб-браузер
if (php_sapi_name() !== 'cli') {
  header('Content-Type: text/html; charset=utf-8');
}

function formatBytes($bytes, $precision = 2) {
  $units = array('B', 'KB', 'MB', 'GB', 'TB');

  for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
    $bytes /= 1024;
  }

  return round($bytes, $precision) . ' ' . $units[$i];
}

function getSystemInfo() {
  $info = array();

  // Основная информация о системе
  $info['os'] = php_uname();
  $info['php_version'] = phpversion();
  $info['server_software'] = $_SERVER['SERVER_SOFTWARE'] ?? 'Неизвестно';
  $info['document_root'] = $_SERVER['DOCUMENT_ROOT'] ?? 'Неизвестно';

  // Информация о процессоре
  if (stripos(PHP_OS, 'linux') !== false) {
    // Linux системы
    $cpuinfo = file_get_contents('/proc/cpuinfo');
    if ($cpuinfo !== false) {
      preg_match_all('/^processor\s+:/m', $cpuinfo, $matches);
      $info['cpu_cores'] = count($matches[0]);

      preg_match('/model name\s+:\s+(.+)/m', $cpuinfo, $matches);
      $info['cpu_model'] = isset($matches[1]) ? trim($matches[1]) : 'Неизвестно';

      preg_match('/cpu MHz\s+:\s+(.+)/m', $cpuinfo, $matches);
      $info['cpu_frequency'] = isset($matches[1]) ? trim($matches[1]) . ' MHz' : 'Неизвестно';
    }

    // Информация о памяти
    $meminfo = file_get_contents('/proc/meminfo');
    if ($meminfo !== false) {
      preg_match('/MemTotal:\s+(\d+)\s+kB/i', $meminfo, $matches);
      $info['total_memory'] = isset($matches[1]) ? formatBytes($matches[1] * 1024) : 'Неизвестно';

      preg_match('/MemFree:\s+(\d+)\s+kB/i', $meminfo, $matches);
      $info['free_memory'] = isset($matches[1]) ? formatBytes($matches[1] * 1024) : 'Неизвестно';

      preg_match('/MemAvailable:\s+(\d+)\s+kB/i', $meminfo, $matches);
      $info['available_memory'] = isset($matches[1]) ? formatBytes($matches[1] * 1024) : 'Неизвестно';

      preg_match('/SwapTotal:\s+(\d+)\s+kB/i', $meminfo, $matches);
      $info['swap_total'] = isset($matches[1]) ? formatBytes($matches[1] * 1024) : 'Неизвестно';

      preg_match('/SwapFree:\s+(\d+)\s+kB/i', $meminfo, $matches);
      $info['swap_free'] = isset($matches[1]) ? formatBytes($matches[1] * 1024) : 'Неизвестно';
    }

    // Информация о дисках
    $info['disk_usage'] = array();
    $df_output = shell_exec('df -h 2>/dev/null');
    if ($df_output) {
      $lines = explode("\n", $df_output);
      foreach ($lines as $line) {
        if (preg_match('/^\/dev\//', $line)) {
          $parts = preg_split('/\s+/', $line);
          if (count($parts) >= 6) {
            $info['disk_usage'][] = array(
              'device' => $parts[0],
              'size' => $parts[1],
              'used' => $parts[2],
              'available' => $parts[3],
              'use_percent' => $parts[4],
              'mount_point' => $parts[5]
            );
          }
        }
      }
    }

    // Загрузка системы
    $loadavg = file_get_contents('/proc/loadavg');
    if ($loadavg !== false) {
      $load = explode(' ', trim($loadavg));
      $info['load_average'] = array(
        '1_min' => $load[0] ?? 'Неизвестно',
        '5_min' => $load[1] ?? 'Неизвестно',
        '15_min' => $load[2] ?? 'Неизвестно'
      );
    }

  } elseif (stripos(PHP_OS, 'win') !== false) {
    // Windows системы
    $wmi_cpu = shell_exec('wmic cpu get name,numberofcores,maxclockspeed /format:csv 2>nul');
    if ($wmi_cpu) {
      $lines = explode("\n", $wmi_cpu);
      foreach ($lines as $line) {
        if (strpos($line, ',') !== false && !empty(trim($line))) {
          $parts = explode(',', $line);
          if (count($parts) >= 4) {
            $info['cpu_cores'] = trim($parts[2]);
            $info['cpu_frequency'] = trim($parts[1]) . ' MHz';
            $info['cpu_model'] = trim($parts[3]);
            break;
          }
        }
      }
    }

    $wmi_memory = shell_exec('wmic computersystem get TotalPhysicalMemory /format:csv 2>nul');
    if ($wmi_memory) {
      preg_match('/\d+/', $wmi_memory, $matches);
      if (isset($matches[0])) {
        $info['total_memory'] = formatBytes($matches[0]);
      }
    }
  }

  // Информация о PHP
  $info['memory_limit'] = ini_get('memory_limit');
  $info['max_execution_time'] = ini_get('max_execution_time');
  $info['upload_max_filesize'] = ini_get('upload_max_filesize');
  $info['post_max_size'] = ini_get('post_max_size');

  // Загруженные расширения
  $info['php_extensions'] = get_loaded_extensions();

  return $info;
}

$systemInfo = getSystemInfo();
?>

<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Информация о системе</title>
  <style>
      body {
          font-family: Arial, sans-serif;
          margin: 20px;
          background-color: #f5f5f5;
      }
      .container {
          max-width: 1200px;
          margin: 0 auto;
          background-color: white;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      }
      h1 {
          color: #333;
          text-align: center;
          margin-bottom: 30px;
      }
      h2 {
          color: #555;
          border-bottom: 2px solid #4CAF50;
          padding-bottom: 5px;
          margin-top: 30px;
      }
      .info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
          margin-bottom: 20px;
      }
      .info-card {
          background-color: #f9f9f9;
          padding: 15px;
          border-radius: 5px;
          border-left: 4px solid #4CAF50;
      }
      .info-row {
          display: flex;
          justify-content: space-between;
          padding: 5px 0;
          border-bottom: 1px solid #eee;
      }
      .info-row:last-child {
          border-bottom: none;
      }
      .label {
          font-weight: bold;
          color: #333;
      }
      .value {
          color: #666;
          text-align: right;
          max-width: 60%;
          word-break: break-word;
      }
      .extensions {
          display: flex;
          flex-wrap: wrap;
          gap: 5px;
          margin-top: 10px;
      }
      .extension {
          background-color: #4CAF50;
          color: white;
          padding: 2px 8px;
          border-radius: 3px;
          font-size: 12px;
      }
      table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 10px;
      }
      th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
      }
      th {
          background-color: #4CAF50;
          color: white;
      }
      tr:nth-child(even) {
          background-color: #f2f2f2;
      }
      .refresh-btn {
          background-color: #4CAF50;
          color: white;
          padding: 10px 20px;
          border: none;
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
          margin-bottom: 20px;
      }
      .refresh-btn:hover {
          background-color: #45a049;
      }
  </style>
</head>
<body>
<div class="container">
  <h1>🖥️ Информация о системе</h1>

  <button class="refresh-btn" onclick="location.reload()">🔄 Обновить информацию</button>

  <div class="info-grid">
    <div class="info-card">
      <h2>🖥️ Основная информация</h2>
      <div class="info-row">
        <span class="label">Операционная система:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['os']); ?></span>
      </div>
      <div class="info-row">
        <span class="label">Версия PHP:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['php_version']); ?></span>
      </div>
      <div class="info-row">
        <span class="label">Веб-сервер:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['server_software']); ?></span>
      </div>
      <div class="info-row">
        <span class="label">Корневая папка:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['document_root']); ?></span>
      </div>
    </div>

    <div class="info-card">
      <h2>🔧 Процессор</h2>
      <div class="info-row">
        <span class="label">Модель:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['cpu_model'] ?? 'Неизвестно'); ?></span>
      </div>
      <div class="info-row">
        <span class="label">Количество ядер:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['cpu_cores'] ?? 'Неизвестно'); ?></span>
      </div>
      <div class="info-row">
        <span class="label">Частота:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['cpu_frequency'] ?? 'Неизвестно'); ?></span>
      </div>
    </div>

    <div class="info-card">
      <h2>💾 Память</h2>
      <div class="info-row">
        <span class="label">Общая память:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['total_memory'] ?? 'Неизвестно'); ?></span>
      </div>
      <div class="info-row">
        <span class="label">Свободная память:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['free_memory'] ?? 'Неизвестно'); ?></span>
      </div>
      <div class="info-row">
        <span class="label">Доступная память:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['available_memory'] ?? 'Неизвестно'); ?></span>
      </div>
      <div class="info-row">
        <span class="label">Общий swap:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['swap_total'] ?? 'Неизвестно'); ?></span>
      </div>
      <div class="info-row">
        <span class="label">Свободный swap:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['swap_free'] ?? 'Неизвестно'); ?></span>
      </div>
    </div>

    <div class="info-card">
      <h2>⚙️ Настройки PHP</h2>
      <div class="info-row">
        <span class="label">Лимит памяти:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['memory_limit']); ?></span>
      </div>
      <div class="info-row">
        <span class="label">Время выполнения:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['max_execution_time']); ?> сек</span>
      </div>
      <div class="info-row">
        <span class="label">Макс. размер файла:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['upload_max_filesize']); ?></span>
      </div>
      <div class="info-row">
        <span class="label">Макс. размер POST:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['post_max_size']); ?></span>
      </div>
    </div>
  </div>

  <?php if (isset($systemInfo['load_average'])): ?>
    <div class="info-card">
      <h2>📊 Загрузка системы</h2>
      <div class="info-row">
        <span class="label">За 1 минуту:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['load_average']['1_min']); ?></span>
      </div>
      <div class="info-row">
        <span class="label">За 5 минут:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['load_average']['5_min']); ?></span>
      </div>
      <div class="info-row">
        <span class="label">За 15 минут:</span>
        <span class="value"><?php echo htmlspecialchars($systemInfo['load_average']['15_min']); ?></span>
      </div>
    </div>
  <?php endif; ?>

  <?php if (!empty($systemInfo['disk_usage'])): ?>
    <div class="info-card">
      <h2>💿 Использование дисков</h2>
      <table>
        <thead>
        <tr>
          <th>Устройство</th>
          <th>Размер</th>
          <th>Использовано</th>
          <th>Доступно</th>
          <th>Использование</th>
          <th>Точка монтирования</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($systemInfo['disk_usage'] as $disk): ?>
          <tr>
            <td><?php echo htmlspecialchars($disk['device']); ?></td>
            <td><?php echo htmlspecialchars($disk['size']); ?></td>
            <td><?php echo htmlspecialchars($disk['used']); ?></td>
            <td><?php echo htmlspecialchars($disk['available']); ?></td>
            <td><?php echo htmlspecialchars($disk['use_percent']); ?></td>
            <td><?php echo htmlspecialchars($disk['mount_point']); ?></td>
          </tr>
        <?php endforeach; ?>
        </tbody>
      </table>
    </div>
  <?php endif; ?>

  <div class="info-card">
    <h2>🔌 Загруженные расширения PHP</h2>
    <div class="extensions">
      <?php foreach ($systemInfo['php_extensions'] as $extension): ?>
        <span class="extension"><?php echo htmlspecialchars($extension); ?></span>
      <?php endforeach; ?>
    </div>
  </div>

  <div style="text-align: center; margin-top: 30px; color: #666; font-size: 14px;">
    <p>Сгенерировано: <?php echo date('d.m.Y H:i:s'); ?></p>
  </div>
</div>
</body>
</html>