{"lossless_reference": {"name": "Lossless Reference (q:v 1)", "description": "Maximum quality lossless reference for comparison", "command_template": "%s -i \"%s\" -vframes 1 -q:v 1 \"%s\"", "frames": {"1": {"success": true, "size_bytes": 201498, "time_seconds": 0.1288, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 196353, "time_seconds": 0.0697, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 198888, "time_seconds": 0.0659, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 201656, "time_seconds": 0.062, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 200540, "time_seconds": 0.0626, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 998935, "avg_time_per_frame": 0.0778, "avg_size_bytes": 199787}, "baseline_premium": {"name": "Baseline Premium (q:v 6)", "description": "Original premium baseline for comparison", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137146, "time_seconds": 0.1955, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134250, "time_seconds": 0.2544, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135879, "time_seconds": 0.2005, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137195, "time_seconds": 0.2049, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136915, "time_seconds": 0.1955, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681385, "avg_time_per_frame": 0.2101, "avg_size_bytes": 136277}, "yuv444_colorspace": {"name": "YUV444 Colorspace (q:v 6)", "description": "Full chroma resolution for better gradients", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 1 -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 142264, "time_seconds": 1.3682, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 139173, "time_seconds": 1.3154, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 140771, "time_seconds": 1.359, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 142314, "time_seconds": 1.3892, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 141845, "time_seconds": 1.4769, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 706367, "avg_time_per_frame": 1.3817, "avg_size_bytes": 141273}, "yuv422_optimized": {"name": "YUV422 Optimized (q:v 6)", "description": "Balanced chroma subsampling for clouds", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -pix_fmt yuvj422p -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 144209, "time_seconds": 0.2235, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 141185, "time_seconds": 0.2193, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 142856, "time_seconds": 0.2144, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 144203, "time_seconds": 0.2248, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 143876, "time_seconds": 0.22, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 716329, "avg_time_per_frame": 0.2204, "avg_size_bytes": 143266}, "dct_algorithm": {"name": "Precise DCT Algorithm (q:v 6)", "description": "High precision DCT for better cloud details", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -dct fastint -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137110, "time_seconds": 0.1905, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134213, "time_seconds": 0.1854, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135917, "time_seconds": 0.1867, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137202, "time_seconds": 0.1891, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136897, "time_seconds": 0.1955, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681339, "avg_time_per_frame": 0.1895, "avg_size_bytes": 136268}, "high_precision_dct": {"name": "High Precision DCT (q:v 6)", "description": "Maximum precision DCT for gradients", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -dct int -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137146, "time_seconds": 0.1965, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134250, "time_seconds": 0.1911, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135879, "time_seconds": 0.197, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137195, "time_seconds": 0.1939, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136915, "time_seconds": 0.193, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681385, "avg_time_per_frame": 0.1943, "avg_size_bytes": 136277}, "advanced_quantization": {"name": "Advanced Quantization (q:v 6)", "description": "Advanced quantization settings for clouds", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq+mv4 -quantizer_noise_shaping 2 -f mjpeg \"%s\"", "frames": {"1": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0476}, "2": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0469}, "3": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0467}, "4": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0479}, "5": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0452}}, "success_count": 0, "total_size_bytes": 0, "avg_time_per_frame": 0, "avg_size_bytes": 0}, "trellis_alternative": {"name": "Trellis Alternative (q:v 6)", "description": "Alternative optimization approach", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -threads 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137146, "time_seconds": 0.1953, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134250, "time_seconds": 0.1896, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135879, "time_seconds": 0.1897, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137195, "time_seconds": 0.189, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136915, "time_seconds": 0.1877, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681385, "avg_time_per_frame": 0.1903, "avg_size_bytes": 136277}, "slightly_better_q5": {"name": "<PERSON><PERSON><PERSON> Q5 (q:v 5)", "description": "q:v 5 with enhanced settings for comparison", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 5 -pix_fmt yuvj422p -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 146743, "time_seconds": 0.3127, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 143660, "time_seconds": 0.3014, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 145227, "time_seconds": 0.3097, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 146732, "time_seconds": 0.3029, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 146288, "time_seconds": 0.3021, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 728650, "avg_time_per_frame": 0.3058, "avg_size_bytes": 145730}}