<?php

/**
 * Alternative Cloud Quality Improvement Methods
 * Альтернативные методы улучшения качества облаков (не через q:v)
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
$configPath = __DIR__ . '/../../config.php';
if (!file_exists($configPath)) {
    die("❌ Config file not found at: {$configPath}\n");
}

$config = require $configPath;

echo "🌤️ === ALTERNATIVE CLOUD QUALITY IMPROVEMENT METHODS ===\n";
echo "📅 " . date('Y-m-d H:i:s') . "\n";
echo "🎯 Base: Premium Baseline (q:v 6) - пробуем альтернативные методы\n\n";

// Test setup
$testResultsDir = __DIR__ . '/alternative_cloud_test_' . date('Y-m-d_H-i-s');
$inputDir = __DIR__ . '/process_pool_mp4/input/camera_1/high-5_mozjpeg';
$ffmpegPath = $config['screenshot']['ffmpeg_path'];

// Check requirements
echo "🔍 === CHECKING REQUIREMENTS ===\n";
echo "✅ FFmpeg found: {$ffmpegPath}\n";

if (!is_dir($inputDir)) {
    die("❌ Input directory not found: {$inputDir}\n");
}

$frameFiles = glob($inputDir . '/*.jpg');
if (count($frameFiles) < 5) {
    die("❌ Need at least 5 frames, found: " . count($frameFiles) . "\n");
}

// Use first 5 frames
$frameFiles = array_slice($frameFiles, 0, 5);
echo "✅ Using 5 frames from: " . basename($inputDir) . "\n";

// Create test directory
if (!mkdir($testResultsDir, 0755, true)) {
    die("❌ Cannot create test directory: {$testResultsDir}\n");
}
echo "✅ Test directory created: " . basename($testResultsDir) . "\n\n";

// Alternative cloud improvement methods
$alternativeMethods = [
    'lossless_reference' => [
        'name' => 'Lossless Reference (q:v 1)',
        'description' => 'Maximum quality lossless reference for comparison',
        'command_template' => '%s -i "%s" -vframes 1 -q:v 1 "%s"',
    ],
    'baseline_premium' => [
        'name' => 'Baseline Premium (q:v 6)',
        'description' => 'Original premium baseline for comparison',
        'command_template' => '%s -i "%s" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg "%s"'
    ],
    'yuv444_colorspace' => [
        'name' => 'YUV444 Colorspace (q:v 6)',
        'description' => 'Full chroma resolution for better gradients',
        'command_template' => '%s -i "%s" -vframes 1 -c:v mjpeg -q:v 6 -pix_fmt yuvj444p -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg "%s"'
    ],
    'yuv422_optimized' => [
        'name' => 'YUV422 Optimized (q:v 6)',
        'description' => 'Balanced chroma subsampling for clouds',
        'command_template' => '%s -i "%s" -vframes 1 -c:v mjpeg -q:v 6 -pix_fmt yuvj422p -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg "%s"'
    ],
    'dct_algorithm' => [
        'name' => 'Precise DCT Algorithm (q:v 6)',
        'description' => 'High precision DCT for better cloud details',
        'command_template' => '%s -i "%s" -vframes 1 -c:v mjpeg -q:v 6 -dct fastint -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg "%s"'
    ],
    'high_precision_dct' => [
        'name' => 'High Precision DCT (q:v 6)',
        'description' => 'Maximum precision DCT for gradients',
        'command_template' => '%s -i "%s" -vframes 1 -c:v mjpeg -q:v 6 -dct int -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg "%s"'
    ],
    'advanced_quantization' => [
        'name' => 'Advanced Quantization (q:v 6)',
        'description' => 'Advanced quantization settings for clouds',
        'command_template' => '%s -i "%s" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq+mv4 -quantizer_noise_shaping 2 -f mjpeg "%s"'
    ],
    'trellis_alternative' => [
        'name' => 'Trellis Alternative (q:v 6)',
        'description' => 'Alternative optimization approach',
        'command_template' => '%s -i "%s" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -threads 1 -f mjpeg "%s"'
    ],
    'slightly_better_q5' => [
        'name' => 'Slightly Better Q5 (q:v 5)',
        'description' => 'q:v 5 with enhanced settings for comparison',
        'command_template' => '%s -i "%s" -vframes 1 -c:v mjpeg -q:v 5 -pix_fmt yuvj422p -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg "%s"'
    ]
];

// Results storage
$results = [];
$overallStartTime = microtime(true);

// Test each alternative method
echo "🌤️ === TESTING ALTERNATIVE CLOUD METHODS ===\n";

foreach ($alternativeMethods as $methodKey => $method) {
    echo "🔍 Testing: {$method['name']}\n";
    echo "   {$method['description']}\n";
    
    $methodDir = $testResultsDir . '/' . $methodKey;
    mkdir($methodDir, 0755, true);
    
    $methodResults = [
        'name' => $method['name'],
        'description' => $method['description'],
        'command_template' => $method['command_template'],
        'frames' => [],
        'success_count' => 0,
        'total_size_bytes' => 0,
        'avg_time_per_frame' => 0,
        'avg_size_bytes' => 0
    ];
    
    $totalTime = 0;
    
    // Test on 5 frames
    foreach ($frameFiles as $index => $frameInput) {
        $frameNumber = $index + 1;
        $outputFile = $methodDir . "/frame_{$frameNumber}.jpg";
        
        $command = sprintf(
            $method['command_template'],
            $ffmpegPath,
            $frameInput,
            $outputFile
        );
        
        $startTime = microtime(true);
        $output = [];
        $returnCode = 0;
        exec($command . ' 2>&1', $output, $returnCode);
        $executionTime = microtime(true) - $startTime;
        
        $totalTime += $executionTime;
        
        if ($returnCode === 0 && file_exists($outputFile)) {
            $fileSize = filesize($outputFile);
            $methodResults['frames'][$frameNumber] = [
                'success' => true,
                'size_bytes' => $fileSize,
                'time_seconds' => round($executionTime, 4),
                'input_file' => basename($frameInput)
            ];
            $methodResults['success_count']++;
            $methodResults['total_size_bytes'] += $fileSize;
            
            echo "   Frame {$frameNumber}: ✅ " . formatBytes($fileSize) . " (" . number_format($executionTime, 3) . "s)\n";
        } else {
            $methodResults['frames'][$frameNumber] = [
                'success' => false,
                'error' => implode(' ', array_slice($output, -1)),
                'time_seconds' => round($executionTime, 4)
            ];
            echo "   Frame {$frameNumber}: ❌ Failed (" . number_format($executionTime, 3) . "s)\n";
            echo "   Error: " . implode(' ', array_slice($output, -2)) . "\n";
        }
    }
    
    // Calculate averages
    if ($methodResults['success_count'] > 0) {
        $methodResults['avg_time_per_frame'] = round($totalTime / 5, 4);
        $methodResults['avg_size_bytes'] = round($methodResults['total_size_bytes'] / $methodResults['success_count']);
    }
    
    $results[$methodKey] = $methodResults;
    
    echo "   Summary: {$methodResults['success_count']}/5 frames, ";
    echo "avg " . formatBytes($methodResults['avg_size_bytes']) . ", ";
    echo "{$methodResults['avg_time_per_frame']}s/frame\n\n";
}

$overallTime = microtime(true) - $overallStartTime;

// Generate comprehensive report
echo "🌤️ === ALTERNATIVE CLOUD METHOD RESULTS ===\n";
echo "⏱️  Total test time: " . round($overallTime, 2) . " seconds\n\n";

printf("| %-30s | %-8s | %-12s | %-10s | %-15s |\n", 
       "Method", "Success", "Avg Size", "Avg Time", "Size vs Baseline");
echo "|" . str_repeat("-", 32) . "|" . str_repeat("-", 10) . "|" . str_repeat("-", 14) . "|" . str_repeat("-", 12) . "|" . str_repeat("-", 17) . "|\n";

$baseline = null;
$successfulMethods = [];

// Find baseline first
foreach ($results as $methodKey => $result) {
    if ($methodKey === 'baseline_premium' && $result['success_count'] > 0) {
        $baseline = $result;
        break;
    }
}

foreach ($results as $methodKey => $result) {
    if ($result['success_count'] > 0) {
        $successfulMethods[] = $result;
        
        $sizeChangePercent = $baseline ? 
            round((($result['avg_size_bytes'] - $baseline['avg_size_bytes']) / $baseline['avg_size_bytes']) * 100, 1) : 0;
        $sizeChangeText = $sizeChangePercent > 0 ? "+{$sizeChangePercent}%" : "{$sizeChangePercent}%";
        
        printf("| %-30s | %-8s | %-12s | %-10s | %-15s |\n",
               substr($result['name'], 0, 30),
               $result['success_count'] . "/5",
               formatBytes($result['avg_size_bytes']),
               $result['avg_time_per_frame'] . "s",
               $sizeChangeText);
    }
}

echo "\n";

// Analysis
if (count($successfulMethods) > 1) {
    echo "🌤️ === ALTERNATIVE METHOD ANALYSIS ===\n";
    
    // Sort by file size
    usort($successfulMethods, function($a, $b) {
        if ($a['avg_size_bytes'] == $b['avg_size_bytes']) return 0;
        return ($a['avg_size_bytes'] < $b['avg_size_bytes']) ? 1 : -1;
    });
    
    echo "🏆 Quality ranking by alternative methods:\n";
    foreach ($successfulMethods as $index => $method) {
        $rank = $index + 1;
        $efficiency = $method['avg_time_per_frame'] > 0 ? 
            round($method['avg_size_bytes'] / $method['avg_time_per_frame'] / 1024, 0) : 0;
        
        // Calculate improvement vs baseline
        $improvement = $baseline ? 
            round((($method['avg_size_bytes'] - $baseline['avg_size_bytes']) / $baseline['avg_size_bytes']) * 100, 1) : 0;
        
        echo "   {$rank}. {$method['name']}\n";
        echo "      Size: " . formatBytes($method['avg_size_bytes']);
        if ($improvement != 0) {
            echo " (" . ($improvement > 0 ? "+" : "") . $improvement . "%)";
        }
        echo ", Time: {$method['avg_time_per_frame']}s, Efficiency: {$efficiency} KB/s\n";
    }
    
    echo "\n💡 === RECOMMENDATIONS FOR CLOUD QUALITY ===\n";
    
    // Find promising methods
    $significantImprovements = [];
    $moderateImprovements = [];
    
    foreach ($successfulMethods as $method) {
        $improvement = $baseline ? 
            (($method['avg_size_bytes'] - $baseline['avg_size_bytes']) / $baseline['avg_size_bytes']) * 100 : 0;
        
        if ($improvement >= 5) {
            $significantImprovements[] = $method;
        } elseif ($improvement >= 2) {
            $moderateImprovements[] = $method;
        }
    }
    
    if (count($significantImprovements) > 0) {
        echo "🌟 Significant improvements (5%+ size increase):\n";
        foreach ($significantImprovements as $method) {
            $improvement = $baseline ? 
                round((($method['avg_size_bytes'] - $baseline['avg_size_bytes']) / $baseline['avg_size_bytes']) * 100, 1) : 0;
            echo "   • {$method['name']}: +{$improvement}%\n";
        }
    }
    
    if (count($moderateImprovements) > 0) {
        echo "⭐ Moderate improvements (2-5% size increase):\n";
        foreach ($moderateImprovements as $method) {
            $improvement = $baseline ? 
                round((($method['avg_size_bytes'] - $baseline['avg_size_bytes']) / $baseline['avg_size_bytes']) * 100, 1) : 0;
            echo "   • {$method['name']}: +{$improvement}%\n";
        }
    }
    
    if (count($significantImprovements) == 0 && count($moderateImprovements) == 0) {
        echo "📊 Result: Baseline Premium (q:v 6) already provides optimal cloud quality\n";
        echo "💡 Альтернативные методы не дают значительного улучшения качества облаков\n";
    }
}

// Save detailed results
$reportFile = $testResultsDir . '/alternative_methods_report.json';
file_put_contents($reportFile, json_encode($results, JSON_PRETTY_PRINT));

echo "\n💾 Detailed report saved: alternative_methods_report.json\n";
echo "📁 All outputs saved in: " . basename($testResultsDir) . "\n";
echo "\n✅ Alternative cloud methods test completed!\n";
echo "🔍 Если результаты все равно не удовлетворяют, возможно нужно попробовать другой кодек\n";

function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

?> 