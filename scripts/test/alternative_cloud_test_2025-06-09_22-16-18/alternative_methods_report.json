{"lossless_reference": {"name": "Lossless Reference (q:v 1)", "description": "Maximum quality lossless reference for comparison", "command_template": "%s -i \"%s\" -vframes 1 -q:v 1 \"%s\"", "frames": {"1": {"success": true, "size_bytes": 201498, "time_seconds": 0.0877, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 196353, "time_seconds": 0.0709, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 198888, "time_seconds": 0.0611, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 201656, "time_seconds": 0.0614, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 200540, "time_seconds": 0.0605, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 998935, "avg_time_per_frame": 0.0683, "avg_size_bytes": 199787}, "baseline_premium": {"name": "Baseline Premium (q:v 6)", "description": "Original premium baseline for comparison", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137146, "time_seconds": 0.1944, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134250, "time_seconds": 0.1943, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135879, "time_seconds": 0.1967, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137195, "time_seconds": 0.1926, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136915, "time_seconds": 0.1906, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681385, "avg_time_per_frame": 0.1937, "avg_size_bytes": 136277}, "yuv444_colorspace": {"name": "YUV444 Colorspace (q:v 6)", "description": "Full chroma resolution for better gradients", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 20 -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 56858, "time_seconds": 0.0946, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 56275, "time_seconds": 0.0984, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 56819, "time_seconds": 0.0959, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 56955, "time_seconds": 0.0955, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 56926, "time_seconds": 0.0952, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 283833, "avg_time_per_frame": 0.0959, "avg_size_bytes": 56767}, "yuv422_optimized": {"name": "YUV422 Optimized (q:v 6)", "description": "Balanced chroma subsampling for clouds", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -pix_fmt yuvj422p -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 144209, "time_seconds": 0.218, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 141185, "time_seconds": 0.205, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 142856, "time_seconds": 0.2192, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 144203, "time_seconds": 0.3182, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 143876, "time_seconds": 0.2278, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 716329, "avg_time_per_frame": 0.2376, "avg_size_bytes": 143266}, "dct_algorithm": {"name": "Precise DCT Algorithm (q:v 6)", "description": "High precision DCT for better cloud details", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -dct fastint -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137110, "time_seconds": 0.192, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134213, "time_seconds": 0.1948, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135917, "time_seconds": 0.1882, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137202, "time_seconds": 0.1882, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136897, "time_seconds": 0.1899, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681339, "avg_time_per_frame": 0.1906, "avg_size_bytes": 136268}, "high_precision_dct": {"name": "High Precision DCT (q:v 6)", "description": "Maximum precision DCT for gradients", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -dct int -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137146, "time_seconds": 0.1941, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134250, "time_seconds": 0.1894, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135879, "time_seconds": 0.1974, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137195, "time_seconds": 0.1938, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136915, "time_seconds": 0.1952, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681385, "avg_time_per_frame": 0.194, "avg_size_bytes": 136277}, "advanced_quantization": {"name": "Advanced Quantization (q:v 6)", "description": "Advanced quantization settings for clouds", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq+mv4 -quantizer_noise_shaping 2 -f mjpeg \"%s\"", "frames": {"1": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0485}, "2": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0469}, "3": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0449}, "4": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0472}, "5": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0472}}, "success_count": 0, "total_size_bytes": 0, "avg_time_per_frame": 0, "avg_size_bytes": 0}, "trellis_alternative": {"name": "Trellis Alternative (q:v 6)", "description": "Alternative optimization approach", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -threads 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137146, "time_seconds": 0.1879, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134250, "time_seconds": 0.1882, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135879, "time_seconds": 0.1907, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137195, "time_seconds": 0.1889, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136915, "time_seconds": 0.186, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681385, "avg_time_per_frame": 0.1883, "avg_size_bytes": 136277}, "slightly_better_q5": {"name": "<PERSON><PERSON><PERSON> Q5 (q:v 5)", "description": "q:v 5 with enhanced settings for comparison", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 5 -pix_fmt yuvj422p -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 146743, "time_seconds": 0.3045, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 143660, "time_seconds": 0.3527, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 145227, "time_seconds": 0.3051, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 146732, "time_seconds": 0.3041, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 146288, "time_seconds": 0.3045, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 728650, "avg_time_per_frame": 0.3142, "avg_size_bytes": 145730}}