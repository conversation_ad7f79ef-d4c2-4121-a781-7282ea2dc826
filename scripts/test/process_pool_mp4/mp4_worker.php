<?php

namespace App\Test;

use Exception;

// Enable comprehensive error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set max execution time to 4 hours for video processing
set_time_limit(14400);

class Mp4Worker {
    private $job;
    private $testResultsDir;
    private $config;
    private $debugMode;

    public function __construct($jobData, $testResultsDir, $config, $debugMode = false) {
        $this->job = $jobData;
        $this->testResultsDir = $testResultsDir;
        $this->config = $config;
        $this->debugMode = $debugMode;
    }

    public function processVideo() {
        $startTime = microtime(true);
        
        try {
            $result = [
                'job_id' => $this->job['job_id'],
                'camera_name' => $this->job['camera_name'],
                'format_name' => $this->job['format_name'],
                'quality_key' => $this->job['quality_key'],
                'success' => false,
                'execution_time' => 0,
                'file_size' => 0,
                'error' => ''
            ];

            echo "🎬 Processing MP4: {$this->job['job_id']}\n";
            echo "📂 Input: {$this->job['format_dir']}\n";
            echo "🎯 Quality: {$this->job['quality']['display_name']} (CRF {$this->job['quality']['crf']})\n";
            
            // Create output directory structure
            $outputDir = $this->createOutputDirectory();
            
            // Generate video filename
            $outputFile = $outputDir . "/{$this->job['camera_name']}_{$this->job['format_name']}_{$this->job['quality_key']}.mp4";
            
            // Skip if video already exists
            if (file_exists($outputFile) && filesize($outputFile) > 1000) {
                $result['success'] = true;
                $result['file_size'] = filesize($outputFile);
                $result['execution_time'] = microtime(true) - $startTime;
                $result['skipped'] = true;
                echo "⏭️ Skipped: Video already exists\n";
                return $result;
            }
            
            // Get frame files for video creation
            $frameFiles = $this->getFrameFiles();
            
            if (empty($frameFiles)) {
                throw new Exception("No frame files found in {$this->job['format_dir']}");
            }
            
            echo "📊 Found " . count($frameFiles) . " frames\n";
            
            // Create video from frames
            $videoResult = $this->createVideoFromFrames($frameFiles, $outputFile);
            
            if ($videoResult['success']) {
                $result['success'] = true;
                $result['file_size'] = filesize($outputFile);
                $result['frame_count'] = count($frameFiles);
                echo "✅ Successfully created: " . basename($outputFile) . " (" . round(filesize($outputFile)/1024/1024, 2) . " MB)\n";
            } else {
                $result['error'] = $videoResult['error'];
                echo "❌ Failed to create video: {$videoResult['error']}\n";
            }
            
            $result['execution_time'] = microtime(true) - $startTime;
            return $result;
            
        } catch (Exception $e) {
            $result = [
                'job_id' => $this->job['job_id'],
                'camera_name' => $this->job['camera_name'],
                'format_name' => $this->job['format_name'],
                'quality_key' => $this->job['quality_key'],
                'success' => false,
                'execution_time' => microtime(true) - $startTime,
                'file_size' => 0,
                'error' => $e->getMessage()
            ];
            
            echo "❌ Exception: " . $e->getMessage() . "\n";
            return $result;
        }
    }

    private function createOutputDirectory() {
        $outputDir = $this->testResultsDir . "/{$this->job['camera_name']}";
        
        if (!file_exists($outputDir)) {
            // Use @ to suppress warnings about directory already existing
            if (!@mkdir($outputDir, 0755, true) && !file_exists($outputDir)) {
                throw new Exception("Failed to create output directory: {$outputDir}");
            }
        }
        
        return $outputDir;
    }

    private function getFrameFiles() {
        $frameFiles = glob($this->job['format_dir'] . '/*.jpg');
        
        if (empty($frameFiles)) {
            return [];
        }
        
        // Sort frames numerically
        sort($frameFiles);
        
        // In debug mode, use only first 30 frames for 1-second video
        if ($this->debugMode) {
            echo "🐛 DEBUG MODE: Using only first 30 frames\n";
            return array_slice($frameFiles, 0, 30);
        }
        
        // Use all 900 frames for 30-second video at 30fps
        return $frameFiles;
    }

    private function createVideoFromFrames($frameFiles, $outputFile) {
        try {
            $frameCount = count($frameFiles);
            $duration = $frameCount / 30; // 30 FPS
            
            echo "🎬 Creating {$duration}s video from {$frameCount} frames at 30 FPS\n";
            
            // Create temporary symlinks with sequential naming for FFmpeg input pattern
            $tempDir = $this->createTempFrameLinks($frameFiles);
            
            // Build FFmpeg command based on screenshots_v2.php implementation
            $inputPattern = $tempDir . '/frame_%05d.jpg';
            
            // Build FFmpeg command with tune support
            $tuneParam = '';
            if (!empty($this->job['quality']['tune'])) {
                $tuneParam = ' -tune ' . escapeshellarg($this->job['quality']['tune']);
            }
            
            $command = sprintf(
                '%s -framerate 30 -i "%s" -t %d -vf "scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2" -c:v libx264 -preset %s%s -crf %d -pix_fmt yuv420p -r 30 -y "%s"',
                escapeshellcmd($this->config['screenshot']['ffmpeg_path']),
                $inputPattern,
                $duration,
                $this->job['quality']['preset'],
                $tuneParam,
                $this->job['quality']['crf'],
                $outputFile
            );

            echo "🔧 FFmpeg Command: " . substr($command, 0, 120) . "...\n";
            
            exec($command . " 2>&1", $output, $returnCode);
            
            // Clean up temporary directory
            $this->cleanupTempDir($tempDir);
            
            if ($returnCode === 0 && file_exists($outputFile) && filesize($outputFile) > 1000) {
                return [
                    'success' => true,
                    'duration' => $duration,
                    'frame_count' => $frameCount
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'FFmpeg failed - Return code: ' . $returnCode . ', Output: ' . implode(' ', array_slice($output, -3))
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Video creation exception: ' . $e->getMessage()
            ];
        }
    }

    private function createTempFrameLinks($frameFiles) {
        $tempDir = $this->testResultsDir . "/temp_frames_" . uniqid();
        
        if (!mkdir($tempDir, 0755, true)) {
            throw new Exception("Failed to create temp directory: {$tempDir}");
        }
        
        foreach ($frameFiles as $index => $frameFile) {
            $linkName = sprintf("%s/frame_%05d.jpg", $tempDir, $index + 1);
            
            if (!symlink($frameFile, $linkName)) {
                // If symlink fails, try copy
                if (!copy($frameFile, $linkName)) {
                    throw new Exception("Failed to create frame link/copy: {$linkName}");
                }
            }
        }
        
        echo "🔗 Created " . count($frameFiles) . " frame links in temp directory\n";
        return $tempDir;
    }

    private function cleanupTempDir($tempDir) {
        if (is_dir($tempDir)) {
            $files = glob($tempDir . '/*');
            foreach ($files as $file) {
                if (is_link($file)) {
                    unlink($file);
                } elseif (is_file($file)) {
                    unlink($file);
                }
            }
            rmdir($tempDir);
            echo "🧹 Cleaned up temp directory\n";
        }
    }
}

// Main worker execution
try {
if ($argc < 2) {
        throw new Exception("Usage: php mp4_worker.php <base64_encoded_job_data>");
    }

    $jobDataEncoded = $argv[1];
    $jobDataDecoded = base64_decode($jobDataEncoded);
    $jobData = json_decode($jobDataDecoded, true);

    if (!$jobData) {
        throw new Exception("Failed to decode job data");
    }

    $worker = new Mp4Worker(
        $jobData['job'],
        $jobData['test_results_dir'],
        $jobData['config'],
        $jobData['debug_mode'] ?? false
    );

    $result = $worker->processVideo();
    
    // Output result as JSON for parent process to parse
    echo "RESULT_JSON:" . json_encode($result) . "\n";
    
} catch (Exception $e) {
    $errorResult = [
        'success' => false,
        'execution_time' => 0,
        'file_size' => 0,
        'error' => $e->getMessage()
    ];
    
    echo "RESULT_JSON:" . json_encode($errorResult) . "\n";
    echo "Worker exception: " . $e->getMessage() . "\n";
}