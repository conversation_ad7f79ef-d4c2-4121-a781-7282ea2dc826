<?php

namespace App\Test;

use PDO;
use Exception;

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

class JpegPoolTester {
    private $config;
    private $db;
    private $testResults = [];
    private $baseDir;
    private $testResultsDir;
    private $testSessionId;
    private $framesPerWorker;
    private $debugMode;

    // JPEG quality configurations
    private $jpegFormats = [
        'lossless' => [
            'name' => 'Lossless JPEG',
            'command_template' => '%s -i "%s" -vframes 1 -q:v 1 "%s"',
            'description' => 'Lossless JPEG with q:v 1',
            'extension' => 'jpg',
            'quality_level' => 'lossless',
            'display_name' => 'Lossless JPEG'
        ],
        // 'high-5' => [
        //     'name' => 'High Quality 5',
        //     'command_template' => '%s -i "%s" -vframes 1 -q:v 5 "%s"',
        //     'description' => 'High quality JPEG with q:v 5',
        //     'extension' => 'jpg',
        //     'quality_level' => 'high-5',
        //     'display_name' => 'High Quality 5'
        // ],
        // 'mozjpeg_postprocess' => [
        //     'name' => 'MozJPEG Post-Process',
        //     'command_template' => '%s -i "%s" -vframes 1 -vf "select=\'eq(pict_type,I)\'" -c:v mjpeg -q:v 5 -f mjpeg "%s.tmp" && /usr/local/bin/cjpeg -quality 75 -progressive -optimize -nojfif "%s.tmp" > "%s" && rm "%s.tmp"',
        //     'description' => 'Basic JPEG -> MozJPEG post-processing (best compression)',
        //     'extension' => 'jpg',
        //     'quality_level' => 'mozjpeg_postprocess',
        //     'display_name' => 'MozJPEG Post-Process'
        // ],
        // 'high-6' => [
        //     'name' => 'High Quality 6',
        //     'command_template' => '%s -probesize 512M -analyzeduration 5000000 -i "%s" -vframes 1 -q:v 6 -pix_fmt yuvj420p -bufsize 64M -map_metadata -1 -f image2 "%s"',
        //     'description' => 'High quality JPEG with q:v 6',
        //     'extension' => 'jpg',
        //     'quality_level' => 'high-6',
        //     'display_name' => 'High Quality 6'
        // ],
        // 'high-7' => [
        //     'name' => 'High Quality 7',
        //     'command_template' => '%s -probesize 512M -analyzeduration 5000000 -i "%s" -vframes 1 -q:v 7 -pix_fmt yuvj420p -bufsize 64M -map_metadata -1 -f image2 "%s"',
        //     'description' => 'High quality JPEG with q:v 7',
        //     'extension' => 'jpg',
        //     'quality_level' => 'high-7',
        //     'display_name' => 'High Quality 7'
        // ],
        // 'high-8' => [
        //     'name' => 'High Quality 8',
        //     'command_template' => '%s -probesize 512M -analyzeduration 5000000 -i "%s" -vframes 1 -q:v 8 -pix_fmt yuvj420p -bufsize 64M -map_metadata -1 -f image2 "%s"',
        //     'description' => 'High quality JPEG with q:v 8',
        //     'extension' => 'jpg',
        //     'quality_level' => 'high-8',
        //     'display_name' => 'High Quality 8'
        // ],
    ];

    public function __construct(array $config, int $framesPerWorker = 1800, bool $debugMode = false) {
        $this->config = $config;
        $this->baseDir = __DIR__;
        $this->testSessionId = date('Y-m-d_H-i-s');
        $this->framesPerWorker = $framesPerWorker;
        $this->debugMode = $debugMode;
        
        // Debug mode doesn't override framesPerWorker - let the caller decide
        
        $this->createDirectoryStructure();
        $this->initializeDatabase();
        
        error_log("JpegPoolTester initialized successfully");
    }

    private function createDirectoryStructure() {
        $this->testResultsDir = $this->baseDir . "/results_{$this->testSessionId}";
        
        $directories = [
            $this->testResultsDir,
            $this->testResultsDir . "/process_pool",
            $this->testResultsDir . "/logs",
            $this->testResultsDir . "/reports"
        ];
        
        foreach ($directories as $dir) {
            if (!file_exists($dir)) {
                if (!mkdir($dir, 0755, true)) {
                    throw new Exception("Failed to create directory: " . $dir);
                }
                error_log("Created directory: " . $dir);
            }
        }

            foreach ($this->jpegFormats as $formatKey => $format) {
            $formatDir = $this->testResultsDir . "/process_pool/{$formatKey}";
                if (!file_exists($formatDir)) {
                    mkdir($formatDir, 0755, true);
                    error_log("Created directory: " . $formatDir);
            }
        }
        
        $this->createReadme();
    }

    private function createReadme() {
        $readmeContent = "# JPEG PROCESS POOL PERFORMANCE TEST\n\n";
        $readmeContent .= "Test Session: {$this->testSessionId}\n";
        $readmeContent .= "Started: " . date('Y-m-d H:i:s') . "\n\n";
        $readmeContent .= "## Configuration\n";
        $readmeContent .= "- Max parallel workers: 6\n";
        $readmeContent .= "- Frames per format: {$this->framesPerWorker}\n";
        $readmeContent .= "- Total formats: " . count($this->jpegFormats) . "\n";
        $readmeContent .= "- Debug mode: " . ($this->debugMode ? 'ON' : 'OFF') . "\n";
        $readmeContent .= "- Processing mode: Sequential by format (max 6 concurrent)\n";
        $readmeContent .= "- Each worker processes one format independently\n\n";
        
        file_put_contents($this->testResultsDir . "/README.md", $readmeContent);
        echo "📄 Created README.md with test documentation\n";
    }

    private function initializeDatabase() {
        try {
            $dsn = sprintf(
                "mysql:host=%s;dbname=%s;charset=%s",
                $this->config['database']['host'],
                $this->config['database']['dbname'],
                $this->config['database']['charset']
            );

            $this->db = new PDO(
                $dsn,
                $this->config['database']['username'],
                $this->config['database']['password'],
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            error_log("Database connection successful for JPEG Pool testing");
        } catch (Exception $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw $e;
        }
    }

    public function runJpegQualityTest() {
        try {
            $overallStartTime = microtime(true);
            
            echo "🚀 === JPEG QUALITY PROCESS POOL TEST ===\n";
            echo "📂 Test Session: {$this->testSessionId}\n";
            echo "🔧 Configuration: 6 workers per format, {$this->framesPerWorker} frames per worker\n";
            if ($this->debugMode) {
                echo "🐛 DEBUG MODE: Testing with minimal frames\n";
            }
            echo "📁 Results Directory: " . basename($this->testResultsDir) . "\n\n";

            echo "⚡ Process Pool approach (6 parallel workers per format)\n";
            $poolStats = $this->testProcessPoolApproach();
            
            echo "\n📊 Generating comprehensive report\n";
            $this->generateReport($poolStats);
            
            $overallTime = microtime(true) - $overallStartTime;
            
            echo "\n✅ === JPEG QUALITY TEST COMPLETED ===\n";
            echo "⏱️ Total test execution time: " . round($overallTime, 2) . "s\n";
            echo "📂 Results saved to: " . basename($this->testResultsDir) . "\n\n";
            
        } catch (Exception $e) {
            error_log("Error in JPEG quality test: " . $e->getMessage());
            echo "❌ Error: " . $e->getMessage() . "\n";
            throw $e;
        }
    }

    private function testProcessPoolApproach() {
        echo "\n🚀 Starting JPEG Process Pool Testing with max 6 parallel workers\n";
        
        // Create ONE global pool with 6 workers for ALL formats
        $pool = new JpegProcessPool(6, $this->testResultsDir, $this->config);
        
        // Create one job per format (12 jobs total)
        $allJobs = [];
        foreach ($this->jpegFormats as $formatKey => $format) {
            $allJobs[] = [
                'format_key' => $formatKey,
                'format_name' => $format['name'],
                'frames_per_worker' => $this->framesPerWorker
            ];
        }
        
        echo "📋 Created " . count($allJobs) . " jobs for processing by max 6 workers\n";
        
        // Process ALL jobs through ONE pool (max 6 workers at once)
        $startTime = microtime(true);
        $results = $pool->processJobs($allJobs);
        $totalTime = microtime(true) - $startTime;
        
        echo "⏱️ Total processing time: " . round($totalTime, 2) . " seconds\n";
        
        // Aggregate results by format
        $stats = [];
        foreach ($results as $result) {
            $formatKey = $result['format_key'];
            
            $stats[$formatKey] = [
                'success_count' => $result['success_count'] ?? 0,
                'error_count' => $result['error_count'] ?? 0,
                'total_size' => $result['total_size'] ?? 0,
                'total_time' => $result['total_time'] ?? 0,
                'average_size' => ($result['success_count'] ?? 0) > 0 ? 
                    ($result['total_size'] ?? 0) / ($result['success_count'] ?? 1) : 0,
                'fps' => ($result['total_time'] ?? 0) > 0 ? 
                    ($result['success_count'] ?? 0) / ($result['total_time'] ?? 1) : 0,
                'worker_result' => $result
            ];
            
            // Display format results
            $formatName = $this->jpegFormats[$formatKey]['display_name'];
            $successCount = $stats[$formatKey]['success_count'];
            $errorCount = $stats[$formatKey]['error_count'];
            $totalSize = $stats[$formatKey]['total_size'];
            $avgSize = round($stats[$formatKey]['average_size'] / 1024, 1);
            $fps = round($stats[$formatKey]['fps'], 1);
            $time = round($stats[$formatKey]['total_time'], 2);
            
            $successRate = $successCount > 0 ? round(($successCount / ($successCount + $errorCount)) * 100, 1) : 0;
            
            echo "✅ Format: {$formatName} | Frames: {$successCount} | Errors: {$errorCount}\n";
            echo "📊 Success: {$successCount}/{$successCount} ({$successRate}%) | Avg size: {$avgSize} KB\n";
            echo "⚡ Performance: {$fps} FPS | Time: {$time}s\n\n";
        }
        
        $this->generateReport($stats);
        return $stats;
    }

    private function generateReport($stats) {
        $reportDir = $this->testResultsDir . "/reports";
        
        // Generate comprehensive report
        $reportContent = "# JPEG Quality Process Pool Test Report\n\n";
        $reportContent .= "Test Session: {$this->testSessionId}\n";
        $reportContent .= "Generated: " . date('Y-m-d H:i:s') . "\n\n";
        
        $reportContent .= "## Test Configuration\n";
        $reportContent .= "- Max parallel workers: 6\n";
        $reportContent .= "- Frames per format: {$this->framesPerWorker}\n";
        $reportContent .= "- Total formats tested: " . count($this->jpegFormats) . "\n";
        $reportContent .= "- Debug mode: " . ($this->debugMode ? 'ON' : 'OFF') . "\n";
        $reportContent .= "- Processing mode: Sequential by format with worker pool\n\n";
        
        $reportContent .= "## Results Summary\n\n";
        $reportContent .= "| Format                    | Success | Time (s) | FPS | Avg Size (KB) | Total Size (MB) |\n";
        $reportContent .= "|---------------------------|---------|----------|-----|---------------|----------------|\n";
        
        $summaryData = [];
        
        foreach ($stats as $formatKey => $formatStats) {
            $avgSizeKB = round($formatStats['average_size'] / 1024, 1);
            $totalSizeMB = round($formatStats['total_size'] / (1024 * 1024), 2);
            
            // Выравниваем название формата до 25 символов
            $formatName = str_pad($this->jpegFormats[$formatKey]['display_name'], 25);
            
            $reportContent .= "| {$formatName} ";
            $reportContent .= "| " . str_pad($formatStats['success_count'], 7) . " ";
            $reportContent .= "| " . str_pad(round($formatStats['total_time'], 2), 8) . " ";
            $reportContent .= "| " . str_pad(round($formatStats['fps'], 1), 3) . " ";
            $reportContent .= "| " . str_pad($avgSizeKB, 13) . " ";
            $reportContent .= "| " . str_pad($totalSizeMB, 14) . " |\n";
            
            $summaryData[$formatKey] = [
                'format' => $this->jpegFormats[$formatKey]['display_name'],
                'quality_level' => $this->jpegFormats[$formatKey]['quality_level'],
                'success_count' => $formatStats['success_count'],
                'error_count' => $formatStats['error_count'],
                'total_time' => $formatStats['total_time'],
                'fps' => $formatStats['fps'],
                'average_size_bytes' => $formatStats['average_size'],
                'average_size_kb' => $avgSizeKB,
                'total_size_bytes' => $formatStats['total_size'],
                'total_size_mb' => $totalSizeMB
            ];
        }
        
        $reportContent .= "\n## Performance Analysis\n\n";
        
        // Find best and worst performers
        $bestFPS = max(array_column($stats, 'fps'));
        $worstFPS = min(array_column($stats, 'fps'));
        $smallestSize = min(array_column($stats, 'average_size'));
        $largestSize = max(array_column($stats, 'average_size'));
        
        foreach ($stats as $formatKey => $formatStats) {
            if ($formatStats['fps'] == $bestFPS) {
                $reportContent .= "**Fastest Processing**: {$this->jpegFormats[$formatKey]['display_name']} (" . round($bestFPS, 1) . " FPS)\n\n";
            }
            if ($formatStats['average_size'] == $smallestSize) {
                $reportContent .= "**Smallest File Size**: {$this->jpegFormats[$formatKey]['display_name']} (" . round($smallestSize / 1024, 1) . " KB avg)\n\n";
            }
            if ($formatStats['average_size'] == $largestSize) {
                $reportContent .= "**Largest File Size**: {$this->jpegFormats[$formatKey]['display_name']} (" . round($largestSize / 1024, 1) . " KB avg)\n\n";
            }
        }
        
        file_put_contents($reportDir . "/comprehensive_report.md", $reportContent);
        file_put_contents($reportDir . "/summary.json", json_encode($summaryData, JSON_PRETTY_PRINT));
        
        echo "\n📊 Reports generated:\n";
        echo "  • {$reportDir}/comprehensive_report.md\n";
        echo "  • {$reportDir}/summary.json\n";
    }

    private function getCamera1() {
        try {
            $stmt = $this->db->prepare("SELECT * FROM cameras WHERE id = 1 LIMIT 1");
            $stmt->execute();
            $camera = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$camera) {
                throw new Exception("Camera 1 not found");
            }
            
            return $camera;
        } catch (Exception $e) {
            error_log("Error getting camera 1: " . $e->getMessage());
            throw $e;
        }
    }
}

// JPEG Process Pool implementation (based on AVIF ProcessPool)
class JpegProcessPool {
    private $maxWorkers;
    private $testResultsDir;
    private $config;
    private $activeProcesses = [];
    private $completedJobs = [];
    private $processTimeout = 21600; // 6 hours timeout for very large JPEG operations (900+ frames)

    public function __construct($maxWorkers, $testResultsDir, $config) {
        $this->maxWorkers = $maxWorkers;
        $this->testResultsDir = $testResultsDir;
        $this->config = $config;
    }

    public function processJobs($jobs) {
        $queue = $jobs;
        $this->completedJobs = [];
        
        echo "  🔧 Starting {$this->maxWorkers} workers for " . count($jobs) . " JPEG jobs\n";
        
        while (!empty($queue) || !empty($this->activeProcesses)) {
            // Start new processes if we have capacity
            while (count($this->activeProcesses) < $this->maxWorkers && !empty($queue)) {
                $job = array_shift($queue);
                $this->startWorker($job);
            }
            
            // Check completed processes
            $this->checkCompletedWorkers();
            usleep(100000); // 0.1 second for JPEG operations
        }
        
        echo "  ✅ All JPEG workers completed\n";
        return $this->completedJobs;
    }

    private function startWorker($job) {
        $jobData = base64_encode(json_encode([
            'job' => $job,
            'test_results_dir' => $this->testResultsDir,
            'config' => $this->config
        ]));
        
        $jobId = $job['format_key'] . '_' . uniqid();
        $logFile = $this->testResultsDir . "/logs/jpeg_worker_{$jobId}.log";
        
        $command = sprintf(
            'php %s/jpeg_worker.php "%s" > %s 2>&1 & echo $!',
            __DIR__,
            $jobData,
            $logFile
        );
        
        $pid = trim(shell_exec($command));
        if ($pid) {
            $this->activeProcesses[$pid] = [
                'job' => $job,
                'start_time' => microtime(true),
                'log_file' => $logFile
            ];
            
            echo "    🚀 Started JPEG worker PID {$pid} for format {$job['format_key']}\n";
        }
    }

    private function checkCompletedWorkers() {
        foreach ($this->activeProcesses as $pid => $processInfo) {
            // Check if process is still running using Docker-compatible method
            $running = $this->isProcessRunning($pid);
            
            if (!$running) {
                $processTime = microtime(true) - $processInfo['start_time'];
                
                // Read result from log file
                $result = $this->readWorkerResult($processInfo['log_file'], $processInfo['job'], $processTime);
                $this->completedJobs[] = $result;
                
                echo "    ✅ JPEG worker PID {$pid} completed in " . round($processTime, 2) . "s\n";
                unset($this->activeProcesses[$pid]);
            } elseif ((microtime(true) - $processInfo['start_time']) > $this->processTimeout) {
                // Kill hung process
                shell_exec("kill -TERM {$pid} 2>/dev/null");
                sleep(2);
                shell_exec("kill -KILL {$pid} 2>/dev/null");
                
                $result = [
                    'format_key' => $processInfo['job']['format_key'],
                    'format_name' => $processInfo['job']['format_name'],
                    'success_count' => 0,
                    'error_count' => 1,
                    'total_time' => $this->processTimeout,
                    'total_size' => 0,
                    'error' => 'Process timeout'
                ];
                $this->completedJobs[] = $result;
                
                echo "    ⚠️ JPEG worker PID {$pid} killed (timeout)\n";
                unset($this->activeProcesses[$pid]);
            }
        }
    }

    private function isProcessRunning($pid) {
        // Docker-compatible process check
        $result = shell_exec("kill -0 {$pid} 2>/dev/null; echo $?");
        return trim($result) === '0';
    }

    private function readWorkerResult($logFile, $job, $processTime) {
        $defaultResult = [
                'format_key' => $job['format_key'],
            'format_name' => $job['format_name'],
                'success_count' => 0,
            'error_count' => 1,
                'total_time' => $processTime,
                'total_size' => 0,
            'error' => 'No result data'
        ];

        if (!file_exists($logFile)) {
            return $defaultResult;
        }

        $logContent = file_get_contents($logFile);
        
        // Look for result JSON in log
        if (preg_match('/RESULT_JSON_START\s*\n(.*?)\nRESULT_JSON_END/s', $logContent, $matches)) {
            $resultData = json_decode(trim($matches[1]), true);
            if ($resultData) {
                $resultData['total_time'] = $processTime;
                return $resultData;
            }
        }

        return $defaultResult;
    }
}