{"lossless_reference": {"name": "Lossless Reference (q:v 1)", "description": "Maximum quality lossless reference for comparison", "command_template": "%s -i \"%s\" -vframes 1 -q:v 1 \"%s\"", "frames": {"1": {"success": true, "size_bytes": 201498, "time_seconds": 0.125, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 196353, "time_seconds": 0.0814, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 198888, "time_seconds": 0.0686, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 201656, "time_seconds": 0.0703, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 200540, "time_seconds": 0.0665, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 998935, "avg_time_per_frame": 0.0824, "avg_size_bytes": 199787}, "baseline_premium": {"name": "Baseline Premium (q:v 6)", "description": "Original premium baseline for comparison", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137146, "time_seconds": 0.1923, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134250, "time_seconds": 0.268, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135879, "time_seconds": 0.2038, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137195, "time_seconds": 0.2064, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136915, "time_seconds": 0.1936, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681385, "avg_time_per_frame": 0.2128, "avg_size_bytes": 136277}, "yuv444_colorspace": {"name": "YUV444 Colorspace (q:v 6)", "description": "Full chroma resolution for better gradients", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -pix_fmt yuvj444p -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 158679, "time_seconds": 0.223, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 155539, "time_seconds": 0.2391, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 157253, "time_seconds": 0.2365, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 158653, "time_seconds": 0.2269, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 158139, "time_seconds": 0.2207, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 788263, "avg_time_per_frame": 0.2292, "avg_size_bytes": 157653}, "yuv422_optimized": {"name": "YUV422 Optimized (q:v 6)", "description": "Balanced chroma subsampling for clouds", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -pix_fmt yuvj422p -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 144209, "time_seconds": 0.2111, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 141185, "time_seconds": 0.2052, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 142856, "time_seconds": 0.2125, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 144203, "time_seconds": 0.2139, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 143876, "time_seconds": 0.2247, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 716329, "avg_time_per_frame": 0.2135, "avg_size_bytes": 143266}, "dct_algorithm": {"name": "Precise DCT Algorithm (q:v 6)", "description": "High precision DCT for better cloud details", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -dct fastint -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137110, "time_seconds": 0.1933, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134213, "time_seconds": 0.1887, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135917, "time_seconds": 0.189, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137202, "time_seconds": 0.1874, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136897, "time_seconds": 0.1874, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681339, "avg_time_per_frame": 0.1891, "avg_size_bytes": 136268}, "high_precision_dct": {"name": "High Precision DCT (q:v 6)", "description": "Maximum precision DCT for gradients", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -dct int -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137146, "time_seconds": 0.1924, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134250, "time_seconds": 0.2045, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135879, "time_seconds": 0.189, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137195, "time_seconds": 0.1912, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136915, "time_seconds": 0.1879, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681385, "avg_time_per_frame": 0.193, "avg_size_bytes": 136277}, "advanced_quantization": {"name": "Advanced Quantization (q:v 6)", "description": "Advanced quantization settings for clouds", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq+mv4 -quantizer_noise_shaping 2 -f mjpeg \"%s\"", "frames": {"1": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0456}, "2": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0466}, "3": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0487}, "4": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0475}, "5": {"success": false, "error": "Conversion failed!", "time_seconds": 0.0483}}, "success_count": 0, "total_size_bytes": 0, "avg_time_per_frame": 0, "avg_size_bytes": 0}, "trellis_alternative": {"name": "Trellis Alternative (q:v 6)", "description": "Alternative optimization approach", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 6 -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -threads 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 137146, "time_seconds": 0.1853, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 134250, "time_seconds": 0.1863, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 135879, "time_seconds": 0.195, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 137195, "time_seconds": 0.2207, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 136915, "time_seconds": 0.2016, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 681385, "avg_time_per_frame": 0.1978, "avg_size_bytes": 136277}, "slightly_better_q5": {"name": "<PERSON><PERSON><PERSON> Q5 (q:v 5)", "description": "q:v 5 with enhanced settings for comparison", "command_template": "%s -i \"%s\" -vframes 1 -c:v mjpeg -q:v 5 -pix_fmt yuvj422p -huffman optimal -mpv_flags +naq -quantizer_noise_shaping 1 -f mjpeg \"%s\"", "frames": {"1": {"success": true, "size_bytes": 146743, "time_seconds": 0.3075, "input_file": "frame_001_high-5.jpg"}, "2": {"success": true, "size_bytes": 143660, "time_seconds": 0.3771, "input_file": "frame_002_high-5.jpg"}, "3": {"success": true, "size_bytes": 145227, "time_seconds": 0.3029, "input_file": "frame_003_high-5.jpg"}, "4": {"success": true, "size_bytes": 146732, "time_seconds": 0.3082, "input_file": "frame_004_high-5.jpg"}, "5": {"success": true, "size_bytes": 146288, "time_seconds": 0.3031, "input_file": "frame_005_high-5.jpg"}}, "success_count": 5, "total_size_bytes": 728650, "avg_time_per_frame": 0.3198, "avg_size_bytes": 145730}}