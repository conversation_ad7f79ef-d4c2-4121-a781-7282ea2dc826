-- Description: Creates tables for extensible asynchronous job processing system
-- Supports: Screenshot capture, future video generation, and other job types

-- Job queue and status tracking table
CREATE TABLE IF NOT EXISTS async_jobs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    job_type ENUM('screenshot', 'video', 'cleanup', 'maintenance') NOT NULL,
    camera_id INT NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'timeout', 'cancelled') DEFAULT 'pending',
    priority INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    worker_pid INT NULL,
    worker_hostname VARCHAR(255) NULL,
    config_json TEXT NULL COMMENT 'Job-specific configuration parameters',
    result_json TEXT NULL COMMENT 'Job execution results and metadata',
    error_message TEXT NULL,
    retry_count INT DEFAULT 0,
    max_retries INT DEFAULT 3,
    timeout_seconds INT DEFAULT 3600,
    
    -- Indexes for performance
    INDEX idx_job_type_status (job_type, status),
    INDEX idx_camera_id (camera_id),
    INDEX idx_status_created (status, created_at),
    INDEX idx_worker_pid (worker_pid),
    INDEX idx_priority_created (priority DESC, created_at ASC),
    
    -- Foreign key constraint (assuming cameras table exists)
    FOREIGN KEY (camera_id) REFERENCES cameras(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Job execution logs table
CREATE TABLE IF NOT EXISTS async_job_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    job_id INT NOT NULL,
    log_level ENUM('debug', 'info', 'warning', 'error', 'critical') NOT NULL,
    message TEXT NOT NULL,
    context_json TEXT NULL COMMENT 'Additional context data for the log entry',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_job_id (job_id),
    INDEX idx_log_level (log_level),
    INDEX idx_created_at (created_at),
    INDEX idx_job_level_created (job_id, log_level, created_at),
    
    -- Foreign key constraint
    FOREIGN KEY (job_id) REFERENCES async_jobs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Job statistics and metrics table (for monitoring and optimization)
CREATE TABLE IF NOT EXISTS async_job_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    job_type VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    total_jobs INT DEFAULT 0,
    completed_jobs INT DEFAULT 0,
    failed_jobs INT DEFAULT 0,
    avg_execution_time_seconds DECIMAL(10,2) DEFAULT 0,
    max_execution_time_seconds INT DEFAULT 0,
    total_execution_time_seconds INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Unique constraint to prevent duplicate entries
    UNIQUE KEY unique_job_type_date (job_type, date),
    
    -- Indexes
    INDEX idx_job_type (job_type),
    INDEX idx_date (date),
    INDEX idx_job_type_date (job_type, date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Job worker registry (for tracking active workers and load balancing)
CREATE TABLE IF NOT EXISTS async_job_workers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    worker_id VARCHAR(100) NOT NULL UNIQUE,
    hostname VARCHAR(255) NOT NULL,
    pid INT NOT NULL,
    job_type VARCHAR(50) NOT NULL,
    status ENUM('active', 'idle', 'stopping', 'stopped') DEFAULT 'idle',
    max_concurrent_jobs INT DEFAULT 1,
    current_job_count INT DEFAULT 0,
    last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_worker_id (worker_id),
    INDEX idx_hostname (hostname),
    INDEX idx_job_type (job_type),
    INDEX idx_status (status),
    INDEX idx_last_heartbeat (last_heartbeat)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial configuration data
INSERT IGNORE INTO async_job_stats (job_type, date, total_jobs, completed_jobs, failed_jobs) 
VALUES 
    ('screenshot', CURDATE(), 0, 0, 0),
    ('video', CURDATE(), 0, 0, 0),
    ('cleanup', CURDATE(), 0, 0, 0);
