#!/usr/bin/env php
<?php

/**
 * Screenshot Worker Process
 * 
 * Individual worker process that handles screenshot capture for a single camera.
 * Designed to run as a background process managed by the JobProcessPool.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set memory limit for image processing
ini_set('memory_limit', '512M');

// Set execution time limit (will be managed by process pool timeout)
set_time_limit(1800); // 30 minutes max

// Import required classes
use App\AsyncJobs\ScreenshotJobWorker;

try {
    // Validate command line arguments
    if ($argc < 2) {
        throw new Exception("Usage: php screenshot_worker.php <base64_encoded_job_data>");
    }

    // Decode job data
    $jobDataEncoded = $argv[1];
    $jobDataJson = base64_decode($jobDataEncoded);

    if ($jobDataJson === false) {
        throw new Exception("Failed to decode job data");
    }

    $jobData = json_decode($jobDataJson, true);
    if ($jobData === null) {
        throw new Exception("Failed to parse job data JSON: " . json_last_error_msg());
    }

    // Validate job data structure
    if (!isset($jobData['job']) || !isset($jobData['config'])) {
        throw new Exception("Invalid job data structure");
    }

    echo "Screenshot worker started - PID: " . getmypid() . "\n";
    echo "Job ID: " . $jobData['job']['id'] . "\n";
    echo "Camera ID: " . $jobData['job']['camera_id'] . "\n";

    // Load autoloader
    $autoloaderPath = __DIR__ . '/../vendor/autoload.php';
    if (!file_exists($autoloaderPath)) {
        throw new Exception("Autoloader not found at: " . $autoloaderPath);
    }
    require $autoloaderPath;

    echo "Autoloader loaded successfully\n";

    // Create and run the worker
    $worker = new ScreenshotJobWorker($jobData);
    echo "Worker instance created\n";

    // Execute the job
    $worker->run();
    echo "Worker execution completed\n";

} catch (Exception $e) {
    // Log error details
    $errorMessage = "Screenshot worker failed: " . $e->getMessage();
    $errorDetails = [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ];

    echo "ERROR: " . $errorMessage . "\n";
    echo "File: " . $errorDetails['file'] . "\n";
    echo "Line: " . $errorDetails['line'] . "\n";
    echo "Trace:\n" . $errorDetails['trace'] . "\n";

    // Output structured error result
    $result = [
        'success' => false,
        'error' => $errorMessage,
        'data' => null
    ];

    echo "WORKER_RESULT:" . json_encode($result, JSON_UNESCAPED_SLASHES) . "\n";
    
    // Exit with error code
    exit(1);
}

// If we reach here, the worker completed successfully
echo "Screenshot worker process completed successfully\n";
exit(0);
