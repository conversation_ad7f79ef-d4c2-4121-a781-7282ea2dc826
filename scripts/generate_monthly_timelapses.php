#!/usr/bin/env php
<?php

/**
 * Monthly Timelapse Generation Script
 * 
 * Generates monthly timelapses from the last 30 days of screenshots.
 * Runs once per day via cron: 0 3 * * *
 * 
 * Usage: php scripts/generate_monthly_timelapses.php [options]
 */

// Set memory and execution limits for video processing
ini_set('memory_limit', '2G');
set_time_limit(7200); // 2 hours max

// Import required classes
use App\AsyncJobs\AsyncJobManager;

echo "🎬 ========================================\n";
echo "   MONTHLY TIMELAPSE GENERATION\n";
echo "========================================\n\n";

try {
    $startTime = microtime(true);
    
    // Parse command line arguments
    $options = getopt('h', ['help', 'cameras:', 'dry-run', 'force']);
    
    if (isset($options['h']) || isset($options['help'])) {
        showHelp();
        exit(0);
    }
    
    // Load autoloader
    $autoloaderPath = __DIR__ . '/../vendor/autoload.php';
    if (!file_exists($autoloaderPath)) {
        throw new Exception("Autoloader file not found at: " . $autoloaderPath);
    }
    require $autoloaderPath;
    echo "✅ Autoloader loaded successfully\n";

    // Load configuration
    $configPath = __DIR__ . '/../config.php';
    if (!file_exists($configPath)) {
        throw new Exception("Config file not found at: " . $configPath);
    }
    $config = require $configPath;
    echo "✅ Configuration loaded successfully\n";

    // Validate video configuration
    if (!($config['async_jobs']['video']['enabled'] ?? false)) {
        throw new Exception("❌ Video generation is disabled in configuration");
    }
    
    if (!($config['async_jobs']['video']['periods']['30d']['enabled'] ?? false)) {
        throw new Exception("❌ Monthly timelapse (30d period) is disabled in configuration");
    }
    
    echo "✅ Video configuration validated\n";

    // Initialize job manager
    $jobManager = new AsyncJobManager($config);
    echo "✅ Job manager initialized\n\n";

    // Parse camera filter
    $cameraIds = null;
    if (isset($options['cameras'])) {
        $cameraIds = array_map('intval', explode(',', $options['cameras']));
        echo "🎯 Filtering to specific cameras: " . implode(', ', $cameraIds) . "\n\n";
    }

    // Check for dry run mode
    $dryRun = isset($options['dry-run']);
    if ($dryRun) {
        echo "🔍 DRY RUN MODE - No actual jobs will be created\n\n";
    }

    // Create monthly timelapse jobs
    echo "📋 Creating monthly timelapse jobs...\n";
    
    if (!$dryRun) {
        $createdJobs = $jobManager->createVideoJobs($cameraIds, ['period' => '30d'], 0);
        
        echo "✅ Created " . count($createdJobs) . " monthly timelapse jobs\n";
        
        if (!empty($createdJobs)) {
            echo "\n📝 Created Jobs:\n";
            foreach ($createdJobs as $job) {
                echo "   • Job #{$job['job_id']}: Camera {$job['camera_id']} ({$job['camera_name']})\n";
            }
        }
        
        // Process the jobs
        echo "\n🚀 Processing monthly timelapse jobs...\n";
        $results = $jobManager->processJobs('video');
        
        // Display results
        $successful = array_filter($results, fn($r) => $r['success'] ?? false);
        $failed = array_filter($results, fn($r) => !($r['success'] ?? false));
        
        echo "\n📊 RESULTS:\n";
        echo "   Total jobs processed: " . count($results) . "\n";
        echo "   Successful: " . count($successful) . "\n";
        echo "   Failed: " . count($failed) . "\n";
        
        if (!empty($failed)) {
            echo "\n❌ Failed Jobs:\n";
            foreach ($failed as $result) {
                echo "   • Camera {$result['camera_id']}: {$result['error']}\n";
            }
        }
        
        if (!empty($successful)) {
            echo "\n✅ Successful Jobs:\n";
            foreach ($successful as $result) {
                $data = $result['data'] ?? [];
                $fileSize = isset($data['file_size']) ? round($data['file_size'] / 1024 / 1024, 2) . ' MB' : 'Unknown';
                echo "   • Camera {$result['camera_id']}: {$data['video_filename']} ({$fileSize})\n";
            }
        }
        
    } else {
        // Dry run - just show what would be created
        echo "✅ Would create monthly timelapse jobs for available cameras\n";
    }

    $totalTime = microtime(true) - $startTime;
    echo "\n⏱️  Total execution time: " . round($totalTime, 2) . " seconds\n";
    echo "🎉 Monthly timelapse generation completed successfully!\n";

} catch (Exception $e) {
    echo "\n❌ ERROR: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " (Line: " . $e->getLine() . ")\n";
    
    if (isset($config['debug']) && $config['debug']) {
        echo "\n🔍 Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
    
    exit(1);
}

/**
 * Show help information
 */
function showHelp(): void {
    echo "Monthly Timelapse Generation Script\n\n";
    echo "USAGE:\n";
    echo "  php scripts/generate_monthly_timelapses.php [options]\n\n";
    echo "OPTIONS:\n";
    echo "  --cameras ID1,ID2   Generate timelapses for specific camera IDs only\n";
    echo "  --dry-run           Show what would be done without actually creating jobs\n";
    echo "  --force             Force generation even if recent timelapses exist\n";
    echo "  -h, --help          Show this help message\n\n";
    echo "EXAMPLES:\n";
    echo "  php scripts/generate_monthly_timelapses.php\n";
    echo "  php scripts/generate_monthly_timelapses.php --cameras 1,2,3\n";
    echo "  php scripts/generate_monthly_timelapses.php --dry-run\n\n";
    echo "CRON SCHEDULE:\n";
    echo "  0 3 * * * /usr/bin/php /path/to/scripts/generate_monthly_timelapses.php\n\n";
}
