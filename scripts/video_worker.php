#!/usr/bin/env php
<?php

/**
 * Video Worker Process (Future Implementation)
 * 
 * Placeholder worker script for future video generation functionality.
 * This will handle individual video generation jobs when implemented.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set memory limit for video processing
ini_set('memory_limit', '2G');

// Set execution time limit (will be managed by process pool timeout)
set_time_limit(7200); // 2 hours max for video processing

// Import required classes
use App\AsyncJobs\VideoJobWorker;

try {
    // Validate command line arguments
    if ($argc < 2) {
        throw new Exception("Usage: php video_worker.php <base64_encoded_job_data>");
    }

    // Decode job data
    $jobDataEncoded = $argv[1];
    $jobDataJson = base64_decode($jobDataEncoded);

    if ($jobDataJson === false) {
        throw new Exception("Failed to decode job data");
    }

    $jobData = json_decode($jobDataJson, true);
    if ($jobData === null) {
        throw new Exception("Failed to parse job data JSON: " . json_last_error_msg());
    }

    // Validate job data structure
    if (!isset($jobData['job']) || !isset($jobData['config'])) {
        throw new Exception("Invalid job data structure");
    }

    echo "Video worker started - PID: " . getmypid() . "\n";
    echo "Job ID: " . $jobData['job']['id'] . "\n";
    echo "Camera ID: " . $jobData['job']['camera_id'] . "\n";

    // Load autoloader
    $autoloaderPath = __DIR__ . '/../vendor/autoload.php';
    if (!file_exists($autoloaderPath)) {
        throw new Exception("Autoloader not found at: " . $autoloaderPath);
    }
    require $autoloaderPath;

    echo "Autoloader loaded successfully\n";

    // Create and run the worker
    $worker = new VideoJobWorker($jobData);
    echo "Worker instance created\n";

    // Execute the job
    $worker->run();
    echo "Worker execution completed\n";

} catch (Exception $e) {
    // Log error details
    $errorMessage = "Video worker failed: " . $e->getMessage();
    $errorDetails = [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ];

    echo "ERROR: " . $errorMessage . "\n";
    echo "File: " . $errorDetails['file'] . "\n";
    echo "Line: " . $errorDetails['line'] . "\n";
    echo "Trace:\n" . $errorDetails['trace'] . "\n";

    // Output structured error result
    $result = [
        'success' => false,
        'error' => $errorMessage,
        'data' => null
    ];

    echo "WORKER_RESULT:" . json_encode($result, JSON_UNESCAPED_SLASHES) . "\n";
    
    // Exit with error code
    exit(1);
}

// If we reach here, the worker completed successfully
echo "Video worker process completed successfully\n";
exit(0);
