<?php

namespace App\AsyncJobs;

use Exception;

/**
 * Generic Process Pool for Async Jobs
 * 
 * Manages a pool of worker processes for any job type. Designed to be extensible
 * and reusable for screenshot capture, video generation, and future job types.
 */
class JobProcessPool {
    private JobConfig $config;
    private JobDatabase $database;
    private JobLogger $logger;
    private string $jobType;
    private int $maxWorkers;
    private int $workerTimeout;
    private array $activeProcesses = [];
    private array $completedJobs = [];
    private string $workerScriptPath;

    public function __construct(
        JobConfig $config,
        JobDatabase $database,
        JobLogger $logger,
        string $jobType,
        string $workerScriptPath
    ) {
        $this->config = $config;
        $this->database = $database;
        $this->logger = $logger;
        $this->jobType = $jobType;
        $this->maxWorkers = $config->getMaxWorkers($jobType);
        $this->workerTimeout = $config->getWorkerTimeout($jobType);
        $this->workerScriptPath = $workerScriptPath;
        
        $this->logger->info("Process pool initialized for job type: {$jobType}", null, [
            'max_workers' => $this->maxWorkers,
            'worker_timeout' => $this->workerTimeout,
            'worker_script' => $this->workerScriptPath
        ]);
    }

    /**
     * Process all pending jobs of this type
     */
    public function processJobs(): array {
        $this->logger->info("Starting job processing for type: {$this->jobType}");
        
        $startTime = microtime(true);
        $processedCount = 0;
        
        try {
            while (true) {
                // Start new workers if we have capacity and pending jobs
                $this->startAvailableWorkers();
                
                // Check for completed workers
                $this->checkCompletedWorkers();
                
                // Break if no active processes and no pending jobs
                if (empty($this->activeProcesses) && !$this->hasPendingJobs()) {
                    break;
                }
                
                // Clean up timed out workers
                $this->cleanupTimedOutWorkers();
                
                // Brief pause to prevent excessive CPU usage
                usleep(100000); // 0.1 second
            }
            
            $totalTime = microtime(true) - $startTime;
            $processedCount = count($this->completedJobs);
            
            $this->logger->info("Job processing completed for type: {$this->jobType}", null, [
                'processed_jobs' => $processedCount,
                'total_time' => round($totalTime, 2),
                'avg_time_per_job' => $processedCount > 0 ? round($totalTime / $processedCount, 2) : 0
            ]);
            
        } catch (Exception $e) {
            $this->logger->error("Error in job processing: " . $e->getMessage(), null, [
                'job_type' => $this->jobType,
                'active_processes' => count($this->activeProcesses)
            ]);
            throw $e;
        }
        
        return $this->completedJobs;
    }

    /**
     * Start new workers for pending jobs if we have capacity
     */
    private function startAvailableWorkers(): void {
        while (count($this->activeProcesses) < $this->maxWorkers) {
            $job = $this->database->getNextJob($this->jobType);
            
            if (!$job) {
                break; // No more pending jobs
            }
            
            $this->startWorker($job);
        }
    }

    /**
     * Start a worker process for a specific job
     */
    private function startWorker(array $job): void {
        $jobId = $job['id'];
        $workerPid = getmypid();
        
        // Mark job as running in database
        if (!$this->database->markJobAsRunning($jobId, $workerPid)) {
            $this->logger->warning("Failed to mark job as running", $jobId);
            return;
        }
        
        // Prepare job data for worker
        $jobData = [
            'job' => $job,
            'config' => $this->config->getAll(),
            'job_type_config' => $this->config->getJobConfig($this->jobType)
        ];
        
        $jobDataEncoded = base64_encode(json_encode($jobData, JSON_UNESCAPED_SLASHES));
        
        // Create unique log file for this worker
        $logFile = $this->getWorkerLogFile($jobId);
        
        // Build worker command
        $command = sprintf(
            'php %s "%s" > %s 2>&1 & echo $!',
            escapeshellarg($this->workerScriptPath),
            $jobDataEncoded,
            escapeshellarg($logFile)
        );
        
        // Execute worker process
        $pid = trim(shell_exec($command));
        
        if ($pid && is_numeric($pid)) {
            $this->activeProcesses[$pid] = [
                'job_id' => $jobId,
                'job_type' => $this->jobType,
                'camera_id' => $job['camera_id'],
                'start_time' => microtime(true),
                'log_file' => $logFile,
                'job_data' => $job
            ];
            
            $this->logger->info("Started worker process", $jobId, [
                'worker_pid' => $pid,
                'camera_id' => $job['camera_id'],
                'log_file' => basename($logFile)
            ]);
        } else {
            $this->logger->error("Failed to start worker process", $jobId);
            $this->database->markJobAsFailed($jobId, "Failed to start worker process");
        }
    }

    /**
     * Check for completed worker processes
     */
    private function checkCompletedWorkers(): void {
        foreach ($this->activeProcesses as $pid => $processInfo) {
            if (!$this->isProcessRunning($pid)) {
                $this->handleCompletedWorker($pid, $processInfo);
                unset($this->activeProcesses[$pid]);
            }
        }
    }

    /**
     * Handle a completed worker process
     */
    private function handleCompletedWorker(int $pid, array $processInfo): void {
        $jobId = $processInfo['job_id'];
        $executionTime = microtime(true) - $processInfo['start_time'];
        
        // Read worker result from log file
        $result = $this->readWorkerResult($processInfo['log_file'], $processInfo);
        
        if ($result['success']) {
            $this->database->markJobAsCompleted($jobId, $result['data']);
            $this->logger->info("Worker completed successfully", $jobId, [
                'worker_pid' => $pid,
                'execution_time' => round($executionTime, 2),
                'result' => $result['data']
            ]);
        } else {
            $this->database->markJobAsFailed($jobId, $result['error']);
            $this->logger->error("Worker failed", $jobId, [
                'worker_pid' => $pid,
                'execution_time' => round($executionTime, 2),
                'error' => $result['error']
            ]);
        }
        
        $this->completedJobs[] = array_merge($processInfo, [
            'success' => $result['success'],
            'execution_time' => $executionTime,
            'result' => $result['data'] ?? null,
            'error' => $result['error'] ?? null
        ]);
        
        // Clean up log file
        if (file_exists($processInfo['log_file'])) {
            unlink($processInfo['log_file']);
        }
    }

    /**
     * Clean up workers that have exceeded timeout
     */
    private function cleanupTimedOutWorkers(): void {
        $currentTime = microtime(true);
        
        foreach ($this->activeProcesses as $pid => $processInfo) {
            $runTime = $currentTime - $processInfo['start_time'];
            
            if ($runTime > $this->workerTimeout) {
                $this->logger->warning("Worker timeout, killing process", $processInfo['job_id'], [
                    'worker_pid' => $pid,
                    'run_time' => round($runTime, 2),
                    'timeout' => $this->workerTimeout
                ]);
                
                // Kill the process
                $this->killProcess($pid);
                
                // Mark job as timed out
                $this->database->markJobAsTimeout($processInfo['job_id']);
                
                // Remove from active processes
                unset($this->activeProcesses[$pid]);
            }
        }
    }

    /**
     * Check if a process is still running
     */
    private function isProcessRunning(int $pid): bool {
        $result = shell_exec("kill -0 {$pid} 2>/dev/null; echo $?");
        return trim($result) === '0';
    }

    /**
     * Kill a process
     */
    private function killProcess(int $pid): void {
        // Try graceful termination first
        shell_exec("kill -TERM {$pid} 2>/dev/null");
        sleep(2);
        
        // Force kill if still running
        if ($this->isProcessRunning($pid)) {
            shell_exec("kill -KILL {$pid} 2>/dev/null");
        }
    }

    /**
     * Check if there are pending jobs
     */
    private function hasPendingJobs(): bool {
        $job = $this->database->getNextJob($this->jobType);
        return $job !== null;
    }

    /**
     * Get log file path for a worker
     */
    private function getWorkerLogFile(int $jobId): string {
        $logDir = __DIR__ . '/../../logs/workers';
        if (!file_exists($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        return $logDir . "/{$this->jobType}_worker_{$jobId}_" . uniqid() . ".log";
    }

    /**
     * Read worker result from log file
     */
    private function readWorkerResult(string $logFile, array $processInfo): array {
        if (!file_exists($logFile)) {
            return [
                'success' => false,
                'error' => 'Worker log file not found',
                'data' => null
            ];
        }
        
        $logContent = file_get_contents($logFile);
        
        // Look for JSON result at the end of the log
        $lines = explode("\n", trim($logContent));
        $lastLine = end($lines);
        
        if (strpos($lastLine, 'WORKER_RESULT:') === 0) {
            $resultJson = substr($lastLine, 14); // Remove "WORKER_RESULT:" prefix
            $result = json_decode($resultJson, true);
            
            if ($result !== null) {
                return $result;
            }
        }
        
        // If no structured result found, check for errors in log
        if (strpos($logContent, 'ERROR:') !== false || strpos($logContent, 'Fatal error') !== false) {
            return [
                'success' => false,
                'error' => 'Worker process encountered errors (check logs)',
                'data' => null
            ];
        }
        
        // Default to success if no errors found
        return [
            'success' => true,
            'error' => null,
            'data' => ['message' => 'Worker completed without structured result']
        ];
    }

    /**
     * Get current pool statistics
     */
    public function getStats(): array {
        return [
            'job_type' => $this->jobType,
            'max_workers' => $this->maxWorkers,
            'active_workers' => count($this->activeProcesses),
            'completed_jobs' => count($this->completedJobs),
            'worker_timeout' => $this->workerTimeout
        ];
    }
}
