# Asynchronous Job Processing System

## Overview

This document describes the new asynchronous job processing system designed to handle camera screenshot capture and future video generation at scale. The system is built to efficiently process 300+ cameras using parallel worker processes.

## Architecture

### Core Components

1. **AsyncJobManager** - Main orchestrator for all job types
2. **JobProcessPool** - Generic process pool for parallel execution
3. **JobWorker** - Base class for all worker implementations
4. **JobConfig** - Centralized configuration management
5. **JobLogger** - Multi-type logging with database persistence
6. **JobDatabase** - Database operations for job tracking

### Job Types

- **Screenshot** - Camera screenshot capture (implemented)
- **Video** - Timelapse video generation (placeholder for future)
- **Cleanup** - System maintenance tasks (placeholder for future)

## Database Schema

The system uses four main tables:

- `async_jobs` - Job queue and status tracking
- `async_job_logs` - Detailed execution logs
- `async_job_stats` - Performance metrics and statistics
- `async_job_workers` - Worker registry and load balancing

## Installation

### 1. Import Database Schema

Manually import the database schema:

```sql
-- Import the SQL file
mysql -u username -p database_name < scripts/migrations/001_create_async_jobs_tables.sql
```

### 2. Update Configuration

Update your `config.php` with the async jobs configuration from `config.php.example`:

```php
'async_jobs' => [
    'screenshot' => [
        'enabled' => true,
        'max_workers' => 4,
        'upload_to_backblaze' => false, // Safety: disabled by default
        // ... other settings
    ]
]
```

### 3. Verify Dependencies

Ensure required tools are installed:
- FFmpeg (`/usr/bin/ffmpeg`)
- cjpeg (`/usr/local/bin/cjpeg`)
- ImageMagick PHP extension
- PDO MySQL extension

## Usage

### Basic Screenshot Capture

```bash
# Process all cameras asynchronously
php scripts/capture_screenshots_async.php

# Process specific cameras
php scripts/capture_screenshots_async.php --cameras 1,2,3

# Cleanup only (no processing)
php scripts/capture_screenshots_async.php --cleanup-only
```

### Legacy Compatibility

The original `capture_screenshots.php` has been updated to automatically use the async system when configured:

```bash
# Will use async system if configured, otherwise falls back to legacy
php scripts/capture_screenshots.php
```

### Programmatic Usage

```php
use App\AsyncJobs\AsyncJobManager;

$config = require 'config.php';
$jobManager = new AsyncJobManager($config);

// Create jobs for all cameras
$jobs = $jobManager->createScreenshotJobs();

// Process the jobs
$results = $jobManager->processJobs('screenshot');

// Check queue status
$status = $jobManager->getQueueStatus();
```

## Configuration

### Screenshot Job Settings

```php
'async_jobs' => [
    'screenshot' => [
        'enabled' => true,
        'max_workers' => 4,                    // Parallel workers
        'worker_timeout' => 1800,              // 30 minutes
        'output_dir' => __DIR__ . '/screenshots',
        'ffmpeg_path' => '/usr/bin/ffmpeg',
        'cjpeg_path' => '/usr/local/bin/cjpeg',
        
        // Optimized command template
        'command_template' => '%s -i "%s" -vframes 1 -vf "select=\'eq(pict_type,I)\'" -c:v mjpeg -q:v 5 -f mjpeg "%s.tmp" && %s -quality 75 -progressive -optimize -nojfif "%s.tmp" > "%s" && rm "%s.tmp"',
        
        'image_variations' => [
            'original' => ['enabled' => true],
            'logo_overlay' => ['enabled' => true],
            'thumbnail_720p' => ['enabled' => true, 'width' => 1280, 'height' => 720],
            'thumbnail_low' => ['enabled' => true, 'width' => 720, 'height' => 405]
        ],
        
        'upload_to_backblaze' => false,        // Safety: disabled
    ]
]
```

### Future Video Generation Settings

```php
'async_jobs' => [
    'video' => [
        'enabled' => false,                    // Will be enabled when implemented
        'max_workers' => 2,
        'worker_timeout' => 7200,              // 2 hours
        'output_dir' => __DIR__ . '/videos',
        'temp_dir' => __DIR__ . '/temp/video',
        
        'formats' => [
            'timelapse_1080p' => [
                'enabled' => true,
                'width' => 1920,
                'height' => 1080,
                'fps' => 30,
                'quality' => 'high',
                'codec' => 'h264'
            ]
        ],
        
        'source_timeframe' => [
            'hours' => 24,                     // Last 24 hours
            'min_images' => 10                 // Minimum images needed
        ]
    ]
]
```

## Safety Features

### Backblaze Upload Protection

All Backblaze upload functionality is **commented out by default** for safety during development:

- `upload_to_backblaze` is set to `false` in configuration
- Upload methods in `ScreenshotJobWorker` are commented out
- Database thumbnail URL updates are disabled

### Error Handling

- Comprehensive logging at all levels
- Automatic job retry mechanism (configurable)
- Worker timeout protection
- Process cleanup on failure
- Database transaction safety

### Resource Management

- Configurable worker limits
- Memory and execution time limits
- Automatic cleanup of temporary files
- Old job and log cleanup

## Monitoring

### Queue Status

```bash
# Check current queue status
php -r "
require 'vendor/autoload.php';
use App\AsyncJobs\AsyncJobManager;
\$config = require 'config.php';
\$manager = new AsyncJobManager(\$config);
print_r(\$manager->getQueueStatus());
"
```

### Database Queries

```sql
-- Check job status
SELECT job_type, status, COUNT(*) as count 
FROM async_jobs 
GROUP BY job_type, status;

-- Recent job logs
SELECT aj.job_type, aj.camera_id, ajl.log_level, ajl.message, ajl.created_at
FROM async_job_logs ajl
JOIN async_jobs aj ON ajl.job_id = aj.id
WHERE ajl.created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY ajl.created_at DESC;

-- Performance statistics
SELECT * FROM async_job_stats 
WHERE date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY date DESC;
```

## Testing

### System Validation

```bash
# Test database schema import and configuration
php scripts/test_async_system.php --config-only

# Test with single camera
php scripts/test_async_system.php --camera 1

# Full system test
php scripts/test_async_system.php
```

## Extending the System

### Adding New Job Types

1. Create a new worker class extending `JobWorker`
2. Implement the `execute()` method
3. Create a worker script (e.g., `custom_worker.php`)
4. Add configuration to the `async_jobs` section
5. Register the job type in `AsyncJobManager`

Example:

```php
// src/AsyncJobs/CustomJobWorker.php
class CustomJobWorker extends JobWorker {
    public function execute(): array {
        // Your custom job logic here
        return ['result' => 'success'];
    }
}

// Configuration
'async_jobs' => [
    'custom' => [
        'enabled' => true,
        'max_workers' => 2,
        'worker_timeout' => 3600
    ]
]
```

### Future Video Generation Implementation

The system is designed to easily accommodate video generation:

1. Enable video jobs in configuration
2. Implement the `VideoJobWorker::execute()` method
3. Add video-specific database tables if needed
4. Create video generation commands and logic

## Performance Optimization

### Recommended Settings

For 300+ cameras:
- `max_workers`: 6-8 (adjust based on server capacity)
- `worker_timeout`: 1800 seconds (30 minutes)
- `log_level`: 'info' (reduce to 'warning' for production)
- `cleanup_completed_jobs_after_days`: 7

### Scaling Considerations

- Monitor server CPU and memory usage
- Adjust worker counts based on performance
- Consider database connection pooling for high loads
- Implement job prioritization if needed
- Use separate servers for different job types

## Troubleshooting

### Common Issues

1. **Jobs stuck in 'running' status**
   - Check for timed out workers
   - Run cleanup: `$jobManager->cleanup()`

2. **Worker processes not starting**
   - Verify worker script permissions
   - Check PHP path and autoloader
   - Review error logs

3. **Database connection issues**
   - Verify database configuration
   - Check connection limits
   - Monitor database performance

### Debug Mode

Enable debug logging:

```php
'async_jobs' => [
    'log_level' => 'debug'
]
```

### Log Locations

- PHP error log: Standard PHP error log location
- Database logs: `async_job_logs` table
- Worker logs: `logs/workers/` directory

## Security Considerations

- All upload functionality disabled by default
- Input validation on all job parameters
- Database prepared statements used throughout
- Worker process isolation
- Configurable timeouts prevent runaway processes

## Future Enhancements

- Web-based monitoring dashboard
- Real-time job progress tracking
- Advanced job scheduling and prioritization
- Distributed processing across multiple servers
- Integration with monitoring systems (Prometheus, etc.)
- API endpoints for external job management
