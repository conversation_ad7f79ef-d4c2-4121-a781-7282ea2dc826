<?php

namespace App\AsyncJobs;

use Exception;
use GuzzleHttp\Client;

/**
 * Screenshot Job Worker
 * 
 * Handles asynchronous screenshot capture for individual cameras.
 * Implements the optimized ffmpeg + cjpeg command pipeline.
 */
class ScreenshotJobWorker extends JobWorker {
    private Client $httpClient;
    private array $tempFiles = [];

    public function __construct(array $jobData) {
        parent::__construct($jobData);
        
        // Initialize HTTP client for logo downloads
        $this->httpClient = new Client([
            'timeout' => 30,
            'connect_timeout' => 10
        ]);
        
        // Validate required configuration
        $this->validateConfig([
            'async_jobs.screenshot.ffmpeg_path',
            'async_jobs.screenshot.output_dir',
            'async_jobs.screenshot.command_template'
        ]);
    }

    /**
     * Execute screenshot capture for the assigned camera
     */
    public function execute(): array {
        $camera = $this->getCameraInfo();
        $timestamp = time();
        
        $this->logProgress("Starting screenshot capture", [
            'camera_name' => $camera['name'],
            'hls_url' => $camera['hls_url']
        ]);

        // Create output directory
        $outputDir = $this->getConfigValue('async_jobs.screenshot.output_dir');
        $this->ensureDirectoryExists($outputDir);

        // Generate base screenshot
        $baseScreenshot = $this->captureBaseScreenshot($camera, $outputDir, $timestamp);

        // Generate image variations
        $variations = $this->generateImageVariations($baseScreenshot, $camera, $outputDir, $timestamp);

        // Upload to Backblaze (if enabled)
        $uploadResults = [];
        if ($this->getConfigValue('async_jobs.screenshot.upload_to_backblaze', false)) {
            $uploadResults = $this->uploadToBackblaze($variations, $camera, $timestamp);
        } else {
            $this->logger->info("Backblaze upload disabled (safety)", $this->jobId);
        }
        
        // Update database with thumbnail URL
        $this->updateCameraThumbnail($camera, $variations, $uploadResults);
        
        // Clean up temporary files
        $this->cleanupFiles($this->tempFiles);
        
        $this->logProgress("Screenshot capture completed successfully", [
            'variations_created' => count($variations),
            'uploads_completed' => count($uploadResults)
        ]);

        return [
            'camera_id' => $this->cameraId,
            'camera_name' => $camera['name'],
            'timestamp' => $timestamp,
            'base_screenshot' => basename($baseScreenshot),
            'variations' => array_map('basename', $variations),
            'upload_results' => $uploadResults,
            'temp_files_cleaned' => count($this->tempFiles)
        ];
    }

    /**
     * Capture base screenshot using optimized ffmpeg + cjpeg pipeline
     */
    private function captureBaseScreenshot(array $camera, string $outputDir, int $timestamp): string {
        $outputFile = $outputDir . '/' . $this->generateFilename('camera', 'jpg', $timestamp);
        
        // Get command template and paths
        $commandTemplate = $this->getConfigValue('async_jobs.screenshot.command_template');
        $ffmpegPath = $this->getConfigValue('async_jobs.screenshot.ffmpeg_path');
        $cjpegPath = $this->getConfigValue('async_jobs.screenshot.cjpeg_path', '/usr/local/bin/cjpeg');
        
        // Build the optimized command
        $command = sprintf(
            $commandTemplate,
            escapeshellcmd($ffmpegPath),    // %s - ffmpeg path
            $camera['hls_url'],             // %s - input HLS URL
            escapeshellarg($outputFile),    // %s - temp file for mjpeg output
            escapeshellcmd($cjpegPath),     // %s - cjpeg path
            escapeshellarg($outputFile),    // %s - temp file input for cjpeg
            escapeshellarg($outputFile),    // %s - final output file
            escapeshellarg($outputFile)     // %s - temp file to remove
        );
        
        $this->logProgress("Executing screenshot command", [
            'output_file' => basename($outputFile)
        ]);
        
        $result = $this->executeCommand($command, "Screenshot capture");
        
        if (!$result['success']) {
            throw new Exception("Screenshot capture failed: " . implode("\n", $result['output']));
        }
        
        if (!file_exists($outputFile) || filesize($outputFile) < 1000) {
            throw new Exception("Screenshot file was not created or is too small: " . $outputFile);
        }
        
        $this->logger->info("Base screenshot captured successfully", $this->jobId, [
            'file_size' => filesize($outputFile),
            'execution_time' => $result['execution_time']
        ]);
        
        return $outputFile;
    }

    /**
     * Generate image variations (logo overlay, thumbnails)
     */
    private function generateImageVariations(string $baseScreenshot, array $camera, string $outputDir, int $timestamp): array {
        $variations = ['original' => $baseScreenshot];
        $imageConfig = $this->getConfigValue('async_jobs.screenshot.image_variations', []);
        
        // Logo overlay
        if ($imageConfig['logo_overlay']['enabled'] ?? false) {
            try {
                $logoPath = $this->createLogoOverlay($baseScreenshot, $camera);
                $variations['logo_overlay'] = $logoPath;
                $this->tempFiles[] = $logoPath;
            } catch (Exception $e) {
                $this->logger->warning("Logo overlay failed: " . $e->getMessage(), $this->jobId);
                $variations['logo_overlay'] = $baseScreenshot; // Use original as fallback
            }
        }
        
        // Thumbnail variations
        foreach (['thumbnail_720p', 'thumbnail_low'] as $thumbType) {
            if ($imageConfig[$thumbType]['enabled'] ?? false) {
                $width = $imageConfig[$thumbType]['width'];
                $height = $imageConfig[$thumbType]['height'];
                
                try {
                    $sourceImage = $variations['logo_overlay'] ?? $baseScreenshot;
                    $thumbPath = $this->resizeImage($sourceImage, $width, $height, $thumbType, $timestamp);
                    $variations[$thumbType] = $thumbPath;
                    $this->tempFiles[] = $thumbPath;
                } catch (Exception $e) {
                    $this->logger->warning("Thumbnail creation failed for {$thumbType}: " . $e->getMessage(), $this->jobId);
                }
            }
        }
        
        return $variations;
    }

    /**
     * Create logo overlay using ImageMagick
     */
    private function createLogoOverlay(string $baseImage, array $camera): string {
        if (!extension_loaded('imagick')) {
            throw new Exception("ImageMagick extension not available");
        }
        
        // Clean camera name for logo URL
        $cleanCameraName = str_replace(' ', '', $camera['name']);
        $logoUrl = "https://deployedweb.com/link_logos/overlays/{$cleanCameraName}.png";
        
        // Download logo
        $logoPath = tempnam(sys_get_temp_dir(), 'logo_');
        $this->tempFiles[] = $logoPath;
        
        $response = $this->httpClient->get($logoUrl, ['sink' => $logoPath]);
        
        if ($response->getStatusCode() !== 200) {
            throw new Exception("Failed to download logo for camera {$camera['name']}");
        }
        
        // Create overlay
        $base = new \Imagick($baseImage);
        $logo = new \Imagick($logoPath);
        
        // Scale logo to match base image dimensions
        $baseWidth = $base->getImageWidth();
        $baseHeight = $base->getImageHeight();
        $logo->scaleImage($baseWidth, $baseHeight, true);
        
        // Overlay the logo
        $base->compositeImage($logo, \Imagick::COMPOSITE_OVER, 0, 0);
        
        // Save result
        $resultPath = tempnam(sys_get_temp_dir(), 'overlay_');
        $base->writeImage($resultPath);
        
        // Clean up
        $base->clear();
        $logo->clear();
        
        $this->logger->debug("Logo overlay created", $this->jobId, [
            'logo_url' => $logoUrl,
            'result_size' => filesize($resultPath)
        ]);
        
        return $resultPath;
    }

    /**
     * Resize image using ImageMagick
     */
    private function resizeImage(string $sourcePath, int $width, int $height, string $type, int $timestamp): string {
        if (!extension_loaded('imagick')) {
            throw new Exception("ImageMagick extension not available");
        }
        
        $image = new \Imagick($sourcePath);
        $image->scaleImage($width, $height, true);
        
        $resizedPath = tempnam(sys_get_temp_dir(), "resized_{$type}_");
        $image->writeImage($resizedPath);
        $image->clear();
        
        $this->logger->debug("Image resized", $this->jobId, [
            'type' => $type,
            'dimensions' => "{$width}x{$height}",
            'result_size' => filesize($resizedPath)
        ]);
        
        return $resizedPath;
    }

    /**
     * Upload variations to Backblaze (commented out for safety)
     */
    private function uploadToBackblaze(array $variations, array $camera, int $timestamp): array {
        // SAFETY: All Backblaze upload code is commented out
        /*
        $uploadResults = [];
        
        // Upload to archive bucket
        $archiveFileName = sprintf("cameras/%d/%d.jpg", $camera['id'], $timestamp);
        $uploadResults['archive'] = $this->uploadFile($variations['original'], $archiveFileName, 'archive');
        
        // Upload to thumbnail bucket
        $thumbnailFileName = sprintf("cameras/%d/latest.jpg", $camera['id']);
        $uploadResults['thumbnail'] = $this->uploadFile($variations['original'], $thumbnailFileName, 'thumbnails');
        
        // Upload logo overlay variations
        if (isset($variations['logo_overlay'])) {
            $wbrcFileName = sprintf("cameras/%d/wbrc.jpg", $camera['id']);
            $uploadResults['wbrc'] = $this->uploadFile($variations['logo_overlay'], $wbrcFileName, 'thumbnails');
        }
        
        return $uploadResults;
        */
        
        $this->logger->info("Backblaze upload skipped (commented out for safety)", $this->jobId);
        return [];
    }

    /**
     * Update camera thumbnail URL in database
     */
    private function updateCameraThumbnail(array $camera, array $variations, array $uploadResults): void {
        // For now, just log that we would update the thumbnail
        // In production, this would update the thumbnail_url field
        
        $this->logger->info("Camera thumbnail update skipped (safety)", $this->jobId, [
            'camera_id' => $camera['id'],
            'variations_available' => array_keys($variations)
        ]);
        
        /*
        // Production code would be:
        if (isset($uploadResults['thumbnail']['url'])) {
            $stmt = $this->db->prepare("UPDATE cameras SET thumbnail_url = ? WHERE id = ?");
            $stmt->execute([$uploadResults['thumbnail']['url'], $camera['id']]);
        }
        */
    }

    /**
     * Cleanup method called on destruction
     */
    public function __destruct() {
        $this->cleanupFiles($this->tempFiles);
    }
}
