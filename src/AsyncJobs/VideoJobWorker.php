<?php

namespace App\AsyncJobs;

use Exception;
use DirectoryIterator;

/**
 * Video Timelapse Job Worker
 *
 * Handles asynchronous timelapse video creation from screenshots.
 * Supports multiple timelapse periods (1d, 7d, 30d) with different frame sampling strategies.
 */
class VideoJobWorker extends JobWorker {

    private string $period;
    private array $periodConfig;
    private string $outputDir;
    private string $tempDir;
    private string $screenshotsDir;
    private string $ffmpegPath;

    public function __construct(array $jobData) {
        parent::__construct($jobData);

        // Validate required configuration for video processing
        $this->validateConfig([
            'async_jobs.video.ffmpeg_path',
            'async_jobs.video.output_dir',
            'async_jobs.video.temp_dir',
            'async_jobs.video.screenshots_dir'
        ]);

        // Extract period from job config
        $this->period = $this->jobConfig['period'] ?? '1d';
        $this->periodConfig = $this->getConfigValue("async_jobs.video.periods.{$this->period}");

        if (empty($this->periodConfig)) {
            throw new Exception("Invalid or missing period configuration: {$this->period}");
        }

        // Initialize paths
        $this->outputDir = $this->getConfigValue('async_jobs.video.output_dir');
        $this->tempDir = $this->getConfigValue('async_jobs.video.temp_dir');
        $this->screenshotsDir = $this->getConfigValue('async_jobs.video.screenshots_dir');
        $this->ffmpegPath = $this->getConfigValue('async_jobs.video.ffmpeg_path');

        // Ensure directories exist
        $this->ensureDirectoryExists($this->outputDir);
        $this->ensureDirectoryExists($this->tempDir);
    }

    /**
     * Execute video timelapse generation for the assigned camera
     */
    public function execute(): array {
        $camera = $this->getCameraInfo();
        $timestamp = time();

        $this->logProgress("Starting timelapse video generation", [
            'camera_name' => $camera['name'],
            'camera_id' => $this->cameraId,
            'period' => $this->period,
            'period_name' => $this->periodConfig['name']
        ]);

        try {
            // Step 1: Collect source screenshots
            $screenshots = $this->collectSourceScreenshots($camera);

            if (empty($screenshots)) {
                throw new Exception("No screenshots found for timelapse generation");
            }

            $this->logger->info("Collected screenshots for timelapse", $this->jobId, [
                'total_screenshots' => count($screenshots),
                'period' => $this->period,
                'source_hours' => $this->periodConfig['source_hours']
            ]);

            // Step 2: Apply frame sampling strategy
            $selectedFrames = $this->applyFrameSampling($screenshots);

            $this->logger->info("Applied frame sampling", $this->jobId, [
                'original_frames' => count($screenshots),
                'selected_frames' => count($selectedFrames),
                'frame_skip' => $this->periodConfig['frame_skip'],
                'target_frames' => $this->periodConfig['target_frames']
            ]);

            // Step 3: Generate video filename
            $videoFilename = $this->generateVideoFilename($camera, $selectedFrames, $timestamp);
            $outputPath = $this->outputDir . '/' . $videoFilename;

            // Step 4: Create symlinks for FFmpeg input
            $inputPattern = $this->createInputSymlinks($selectedFrames);

            // Step 5: Generate timelapse video
            $videoResult = $this->generateTimelapse($inputPattern, $outputPath);

            // Step 6: Upload to Backblaze (if enabled)
            $uploadResult = [];
            if ($this->getConfigValue('async_jobs.video.backblaze.enabled', false)) {
                $uploadResult = $this->uploadToBackblaze($outputPath, $videoFilename);
            } else {
                $this->logger->info("Backblaze upload disabled (safety)", $this->jobId);
            }

            // Step 7: Clean up temporary files
            $this->cleanupTempFiles($inputPattern);

            // Step 8: Clean up old videos
            $this->cleanupOldVideos($camera);

            $result = [
                'camera_id' => $this->cameraId,
                'camera_name' => $camera['name'],
                'period' => $this->period,
                'timestamp' => $timestamp,
                'video_filename' => $videoFilename,
                'video_path' => $outputPath,
                'source_images_count' => count($screenshots),
                'selected_frames_count' => count($selectedFrames),
                'video_duration' => $this->periodConfig['video_duration'],
                'file_size' => file_exists($outputPath) ? filesize($outputPath) : 0,
                'upload_result' => $uploadResult,
                'temp_files_cleaned' => true
            ];

            $this->logProgress("Timelapse video generation completed successfully", [
                'video_filename' => $videoFilename,
                'file_size' => $result['file_size'],
                'frames_used' => count($selectedFrames)
            ]);

            return $result;

        } catch (Exception $e) {
            $this->logger->error("Timelapse generation failed: " . $e->getMessage(), $this->jobId, [
                'camera_id' => $this->cameraId,
                'period' => $this->period,
                'error_trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Collect source screenshots for video generation using efficient timestamp filtering
     *
     * @param array $camera Camera information
     * @return array List of screenshot files with timestamps
     */
    private function collectSourceScreenshots(array $camera): array {
        $cameraDir = $this->screenshotsDir . '/camera_' . $camera['id'];

        if (!is_dir($cameraDir)) {
            throw new Exception("Screenshots directory not found: {$cameraDir}");
        }

        $sourceHours = $this->periodConfig['source_hours'];
        $cutoffTimestamp = time() - ($sourceHours * 3600);

        $screenshots = [];
        $iterator = new DirectoryIterator($cameraDir);

        foreach ($iterator as $file) {
            if ($file->isDot() || !$file->isFile()) {
                continue;
            }

            $filename = $file->getFilename();

            // Extract timestamp from filename: camera_1_1234567890.jpg
            if (preg_match('/camera_\d+_(\d+)\.jpg$/', $filename, $matches)) {
                $fileTimestamp = (int)$matches[1];

                // Only include files within our timeframe
                if ($fileTimestamp >= $cutoffTimestamp) {
                    $screenshots[] = [
                        'filename' => $filename,
                        'path' => $file->getPathname(),
                        'timestamp' => $fileTimestamp,
                        'size' => $file->getSize()
                    ];
                }
            }
        }

        // Sort by timestamp (oldest first for proper timelapse sequence)
        usort($screenshots, function($a, $b) {
            return $a['timestamp'] <=> $b['timestamp'];
        });

        $this->logger->debug("Screenshot collection completed", $this->jobId, [
            'total_found' => count($screenshots),
            'cutoff_timestamp' => $cutoffTimestamp,
            'source_hours' => $sourceHours,
            'oldest_timestamp' => !empty($screenshots) ? $screenshots[0]['timestamp'] : null,
            'newest_timestamp' => !empty($screenshots) ? end($screenshots)['timestamp'] : null
        ]);

        return $screenshots;
    }

    /**
     * Apply frame sampling strategy based on period configuration
     *
     * @param array $screenshots All available screenshots
     * @return array Selected screenshots for timelapse
     */
    private function applyFrameSampling(array $screenshots): array {
        $frameSkip = $this->periodConfig['frame_skip'];
        $targetFrames = $this->periodConfig['target_frames'];

        if ($frameSkip <= 1) {
            // Use every frame, but limit to target count
            $selected = array_slice($screenshots, 0, $targetFrames);
        } else {
            // Apply frame skipping algorithm
            $selected = [];
            $totalFrames = count($screenshots);

            if ($totalFrames <= $targetFrames) {
                // Not enough frames to skip, use all
                $selected = $screenshots;
            } else {
                // Calculate optimal skip interval
                $skipInterval = max(1, intval($totalFrames / $targetFrames));

                for ($i = 0; $i < $totalFrames && count($selected) < $targetFrames; $i += $skipInterval) {
                    $selected[] = $screenshots[$i];
                }
            }
        }

        $this->logger->debug("Frame sampling applied", $this->jobId, [
            'original_count' => count($screenshots),
            'selected_count' => count($selected),
            'frame_skip_config' => $frameSkip,
            'target_frames' => $targetFrames,
            'actual_skip_interval' => isset($skipInterval) ? $skipInterval : 1
        ]);

        return $selected;
    }

    /**
     * Generate video filename following the specified convention
     *
     * @param array $camera Camera information
     * @param array $selectedFrames Selected frames for video
     * @param int $timestamp Current timestamp
     * @return string Generated filename
     */
    private function generateVideoFilename(array $camera, array $selectedFrames, int $timestamp): string {
        $firstFrameTimestamp = $selectedFrames[0]['timestamp'];
        $lastFrameTimestamp = end($selectedFrames)['timestamp'];

        return sprintf(
            'camera_%d_%s_%d_%d.mp4',
            $camera['id'],
            $this->period,
            $firstFrameTimestamp,
            $lastFrameTimestamp
        );
    }

    /**
     * Create symlinks for FFmpeg input pattern
     *
     * @param array $selectedFrames Selected frames for video
     * @return string Input pattern for FFmpeg
     */
    private function createInputSymlinks(array $selectedFrames): string {
        $tempInputDir = $this->tempDir . '/input_' . $this->jobId . '_' . time();
        $this->ensureDirectoryExists($tempInputDir);

        $frameNumber = 0;
        foreach ($selectedFrames as $frame) {
            $symlinkPath = sprintf('%s/frame_%06d.jpg', $tempInputDir, $frameNumber);

            if (!symlink($frame['path'], $symlinkPath)) {
                throw new Exception("Failed to create symlink: {$symlinkPath}");
            }

            $frameNumber++;
        }

        $this->logger->debug("Created input symlinks", $this->jobId, [
            'temp_dir' => $tempInputDir,
            'frame_count' => $frameNumber,
            'pattern' => $tempInputDir . '/frame_%06d.jpg'
        ]);

        return $tempInputDir . '/frame_%06d.jpg';
    }

    /**
     * Generate timelapse video using FFmpeg
     *
     * @param string $inputPattern FFmpeg input pattern
     * @param string $outputPath Output video file path
     * @return array Video generation results
     */
    private function generateTimelapse(string $inputPattern, string $outputPath): array {
        $commandTemplate = $this->getConfigValue('async_jobs.video.command_template');
        $framerate = $this->periodConfig['framerate'];
        $duration = $this->periodConfig['video_duration'];
        $crf = $this->periodConfig['crf_quality'];

        // Build FFmpeg command
        $command = sprintf(
            $commandTemplate,
            escapeshellcmd($this->ffmpegPath),
            $framerate,
            $inputPattern,
            $duration,
            $crf,
            escapeshellarg($outputPath)
        );

        $this->logger->info("Starting FFmpeg video generation", $this->jobId, [
            'command' => $command,
            'output_path' => $outputPath,
            'framerate' => $framerate,
            'duration' => $duration,
            'crf_quality' => $crf
        ]);

        $startTime = microtime(true);

        // Execute FFmpeg command
        $output = [];
        $returnCode = 0;
        exec($command . ' 2>&1', $output, $returnCode);

        $executionTime = microtime(true) - $startTime;
        $outputText = implode("\n", $output);

        if ($returnCode !== 0) {
            throw new Exception("FFmpeg failed with return code {$returnCode}: {$outputText}");
        }

        if (!file_exists($outputPath)) {
            throw new Exception("Video file was not created: {$outputPath}");
        }

        $fileSize = filesize($outputPath);

        $this->logger->info("Video generation completed successfully", $this->jobId, [
            'output_path' => $outputPath,
            'file_size' => $fileSize,
            'execution_time' => round($executionTime, 2),
            'return_code' => $returnCode
        ]);

        return [
            'success' => true,
            'output_file' => $outputPath,
            'file_size' => $fileSize,
            'execution_time' => $executionTime,
            'ffmpeg_output' => $outputText
        ];
    }

    /**
     * Upload video to Backblaze B2 storage
     *
     * @param string $videoFile Local video file path
     * @param string $filename Remote filename
     * @return array Upload results
     */
    private function uploadToBackblaze(string $videoFile, string $filename): array {
        // SAFETY: This upload functionality is commented out for safety
        // Uncomment and configure when ready for production use

        /*
        try {
            $bucketName = $this->getConfigValue('async_jobs.video.backblaze.bucket_name');
            $folderPrefix = $this->getConfigValue('async_jobs.video.backblaze.folder_prefix');
            $remotePath = $folderPrefix . $filename;

            // Initialize Backblaze B2 client (implementation needed)
            // $b2Client = new BackblazeB2Client($this->config['backblaze']);

            $this->logger->info("Starting Backblaze upload", $this->jobId, [
                'local_file' => $videoFile,
                'remote_path' => $remotePath,
                'bucket' => $bucketName
            ]);

            // Upload file to Backblaze
            // $uploadResult = $b2Client->uploadFile($bucketName, $remotePath, $videoFile);

            $this->logger->info("Backblaze upload completed", $this->jobId, [
                'remote_url' => $uploadResult['url'],
                'file_size' => filesize($videoFile)
            ]);

            return [
                'success' => true,
                'url' => $uploadResult['url'],
                'remote_path' => $remotePath,
                'file_size' => filesize($videoFile)
            ];

        } catch (Exception $e) {
            $this->logger->error("Backblaze upload failed: " . $e->getMessage(), $this->jobId);
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'url' => '',
                'file_size' => 0
            ];
        }
        */

        $this->logger->info("Backblaze upload skipped (commented out for safety)", $this->jobId, [
            'local_file' => $videoFile,
            'filename' => $filename
        ]);

        return [
            'success' => false,
            'skipped' => true,
            'reason' => 'Upload functionality commented out for safety',
            'url' => '',
            'file_size' => file_exists($videoFile) ? filesize($videoFile) : 0
        ];
    }

    /**
     * Update database with video information
     * 
     * @param array $camera Camera information
     * @param array $videoResults Video generation results
     */
    private function updateVideoDatabase(array $camera, array $videoResults): void {
        // TODO: Implement database update logic
        // This would store video URLs and metadata in the database
    }

    /**
     * Clean up temporary files and directories
     *
     * @param string $inputPattern FFmpeg input pattern (contains temp directory)
     * @return void
     */
    private function cleanupTempFiles(string $inputPattern): void {
        // Extract temp directory from input pattern
        $tempDir = dirname($inputPattern);

        if (strpos($tempDir, $this->tempDir) === 0 && is_dir($tempDir)) {
            $this->logger->debug("Cleaning up temporary directory", $this->jobId, [
                'temp_dir' => $tempDir
            ]);

            // Remove all files in temp directory
            $files = glob($tempDir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                } elseif (is_link($file)) {
                    unlink($file); // Remove symlinks
                }
            }

            // Remove the directory itself
            rmdir($tempDir);

            $this->logger->debug("Temporary files cleaned up", $this->jobId, [
                'files_removed' => count($files),
                'temp_dir_removed' => $tempDir
            ]);
        }
    }

    /**
     * Clean up old videos based on retention policy
     *
     * @param array $camera Camera information
     * @return void
     */
    private function cleanupOldVideos(array $camera): void {
        $retentionDays = $this->periodConfig['old_videos_days'];
        $cutoffTimestamp = time() - ($retentionDays * 24 * 3600);

        $pattern = sprintf(
            '%s/camera_%d_%s_*.mp4',
            $this->outputDir,
            $camera['id'],
            $this->period
        );

        $videoFiles = glob($pattern);
        $removedCount = 0;

        foreach ($videoFiles as $videoFile) {
            $filename = basename($videoFile);

            // Extract timestamp from filename: camera_1_1d_1234567890_1234567890.mp4
            if (preg_match('/camera_\d+_[^_]+_(\d+)_\d+\.mp4$/', $filename, $matches)) {
                $videoTimestamp = (int)$matches[1];

                if ($videoTimestamp < $cutoffTimestamp) {
                    if (unlink($videoFile)) {
                        $removedCount++;
                        $this->logger->debug("Removed old video", $this->jobId, [
                            'filename' => $filename,
                            'timestamp' => $videoTimestamp,
                            'age_days' => round((time() - $videoTimestamp) / 86400, 1)
                        ]);
                    }
                }
            }
        }

        if ($removedCount > 0) {
            $this->logger->info("Old videos cleanup completed", $this->jobId, [
                'removed_count' => $removedCount,
                'retention_days' => $retentionDays,
                'period' => $this->period
            ]);
        }
    }

    /**
     * Ensure directory exists, create if necessary
     *
     * @param string $directory Directory path
     * @return void
     */
    private function ensureDirectoryExists(string $directory): void {
        if (!is_dir($directory)) {
            if (!mkdir($directory, 0755, true)) {
                throw new Exception("Failed to create directory: {$directory}");
            }
            $this->logger->debug("Created directory", $this->jobId, ['directory' => $directory]);
        }
    }
}
