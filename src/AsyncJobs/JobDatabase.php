<?php

namespace App\AsyncJobs;

use PDO;
use Exception;

/**
 * Database Operations for Async Jobs
 * 
 * Handles all database interactions for the async job processing system.
 * Provides methods for job queue management, status tracking, and statistics.
 */
class JobDatabase {
    private PDO $db;
    private JobConfig $config;

    public function __construct(PDO $db, JobConfig $config) {
        $this->db = $db;
        $this->config = $config;
    }

    /**
     * Create a new job in the queue
     */
    public function createJob(string $jobType, int $cameraId, array $config = [], int $priority = 0): int {
        $maxRetries = $this->config->get('async_jobs.job_retry_limit', 3);
        $timeout = $this->config->getWorkerTimeout($jobType);
        
        $stmt = $this->db->prepare("
            INSERT INTO async_jobs (
                job_type, camera_id, priority, config_json, max_retries, timeout_seconds
            ) VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $configJson = !empty($config) ? json_encode($config, JSON_UNESCAPED_SLASHES) : null;
        
        $stmt->execute([
            $jobType,
            $cameraId,
            $priority,
            $configJson,
            $maxRetries,
            $timeout
        ]);
        
        return (int) $this->db->lastInsertId();
    }

    /**
     * Get next available job for processing
     */
    public function getNextJob(string $jobType): ?array {
        $stmt = $this->db->prepare("
            SELECT * FROM async_jobs 
            WHERE job_type = ? 
            AND status = 'pending' 
            AND retry_count < max_retries
            ORDER BY priority DESC, created_at ASC 
            LIMIT 1
        ");
        
        $stmt->execute([$jobType]);
        $job = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $job ?: null;
    }

    /**
     * Mark job as running
     */
    public function markJobAsRunning(int $jobId, int $workerPid, string $hostname = null): bool {
        $stmt = $this->db->prepare("
            UPDATE async_jobs 
            SET status = 'running', 
                started_at = NOW(), 
                worker_pid = ?,
                worker_hostname = ?
            WHERE id = ? AND status = 'pending'
        ");
        
        $hostname = $hostname ?: gethostname();
        $stmt->execute([$workerPid, $hostname, $jobId]);
        
        return $stmt->rowCount() > 0;
    }

    /**
     * Mark job as completed
     */
    public function markJobAsCompleted(int $jobId, array $result = []): bool {
        $resultJson = !empty($result) ? json_encode($result, JSON_UNESCAPED_SLASHES) : null;
        
        $stmt = $this->db->prepare("
            UPDATE async_jobs 
            SET status = 'completed', 
                completed_at = NOW(), 
                result_json = ?
            WHERE id = ?
        ");
        
        $stmt->execute([$resultJson, $jobId]);
        return $stmt->rowCount() > 0;
    }

    /**
     * Mark job as failed
     */
    public function markJobAsFailed(int $jobId, string $errorMessage, bool $shouldRetry = true): bool {
        if ($shouldRetry) {
            // Increment retry count and reset to pending if under limit
            $stmt = $this->db->prepare("
                UPDATE async_jobs 
                SET status = CASE 
                    WHEN retry_count + 1 < max_retries THEN 'pending'
                    ELSE 'failed'
                END,
                retry_count = retry_count + 1,
                error_message = ?,
                worker_pid = NULL,
                worker_hostname = NULL
                WHERE id = ?
            ");
        } else {
            // Mark as permanently failed
            $stmt = $this->db->prepare("
                UPDATE async_jobs 
                SET status = 'failed', 
                    completed_at = NOW(), 
                    error_message = ?
                WHERE id = ?
            ");
        }
        
        $stmt->execute([$errorMessage, $jobId]);
        return $stmt->rowCount() > 0;
    }

    /**
     * Mark job as timed out
     */
    public function markJobAsTimeout(int $jobId): bool {
        $stmt = $this->db->prepare("
            UPDATE async_jobs 
            SET status = 'timeout', 
                completed_at = NOW(),
                worker_pid = NULL,
                worker_hostname = NULL
            WHERE id = ?
        ");
        
        $stmt->execute([$jobId]);
        return $stmt->rowCount() > 0;
    }

    /**
     * Get job by ID
     */
    public function getJob(int $jobId): ?array {
        $stmt = $this->db->prepare("SELECT * FROM async_jobs WHERE id = ?");
        $stmt->execute([$jobId]);
        
        $job = $stmt->fetch(PDO::FETCH_ASSOC);
        return $job ?: null;
    }

    /**
     * Get jobs by status
     */
    public function getJobsByStatus(string $status, string $jobType = null, int $limit = 100): array {
        $whereClause = "WHERE status = ?";
        $params = [$status];
        
        if ($jobType) {
            $whereClause .= " AND job_type = ?";
            $params[] = $jobType;
        }
        
        $stmt = $this->db->prepare("
            SELECT * FROM async_jobs 
            {$whereClause}
            ORDER BY created_at DESC 
            LIMIT ?
        ");
        
        $params[] = $limit;
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get running jobs that may have timed out
     */
    public function getTimedOutJobs(): array {
        $stmt = $this->db->prepare("
            SELECT * FROM async_jobs 
            WHERE status = 'running' 
            AND started_at < DATE_SUB(NOW(), INTERVAL timeout_seconds SECOND)
        ");
        
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Clean up completed jobs older than retention period
     */
    public function cleanupOldJobs(): int {
        $retentionDays = $this->config->get('async_jobs.cleanup_completed_jobs_after_days', 7);
        
        $stmt = $this->db->prepare("
            DELETE FROM async_jobs 
            WHERE status IN ('completed', 'failed', 'timeout') 
            AND completed_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        
        $stmt->execute([$retentionDays]);
        return $stmt->rowCount();
    }

    /**
     * Get job queue statistics
     */
    public function getQueueStats(string $jobType = null): array {
        $whereClause = "";
        $params = [];
        
        if ($jobType) {
            $whereClause = "WHERE job_type = ?";
            $params[] = $jobType;
        }
        
        $stmt = $this->db->prepare("
            SELECT 
                job_type,
                status,
                COUNT(*) as count
            FROM async_jobs 
            {$whereClause}
            GROUP BY job_type, status
            ORDER BY job_type, status
        ");
        
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Update job statistics
     */
    public function updateJobStats(string $jobType, array $stats): void {
        $stmt = $this->db->prepare("
            INSERT INTO async_job_stats (
                job_type, date, total_jobs, completed_jobs, failed_jobs,
                avg_execution_time_seconds, max_execution_time_seconds, total_execution_time_seconds
            ) VALUES (?, CURDATE(), ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
                total_jobs = total_jobs + VALUES(total_jobs),
                completed_jobs = completed_jobs + VALUES(completed_jobs),
                failed_jobs = failed_jobs + VALUES(failed_jobs),
                avg_execution_time_seconds = (
                    (avg_execution_time_seconds * (total_jobs - VALUES(total_jobs))) + 
                    VALUES(total_execution_time_seconds)
                ) / total_jobs,
                max_execution_time_seconds = GREATEST(max_execution_time_seconds, VALUES(max_execution_time_seconds)),
                total_execution_time_seconds = total_execution_time_seconds + VALUES(total_execution_time_seconds),
                updated_at = NOW()
        ");
        
        $stmt->execute([
            $jobType,
            $stats['total_jobs'] ?? 0,
            $stats['completed_jobs'] ?? 0,
            $stats['failed_jobs'] ?? 0,
            $stats['avg_execution_time'] ?? 0,
            $stats['max_execution_time'] ?? 0,
            $stats['total_execution_time'] ?? 0
        ]);
    }

    /**
     * Get cameras that need screenshot jobs
     */
    public function getCamerasForScreenshots(): array {
        $stmt = $this->db->query("
            SELECT id, name, hls_url 
            FROM cameras 
            WHERE hls_url IS NOT NULL 
            ORDER BY name
        ");
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Check if a job already exists for a camera
     */
    public function hasActiveJob(string $jobType, int $cameraId): bool {
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count 
            FROM async_jobs 
            WHERE job_type = ? 
            AND camera_id = ? 
            AND status IN ('pending', 'running')
        ");
        
        $stmt->execute([$jobType, $cameraId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result['count'] > 0;
    }
}
