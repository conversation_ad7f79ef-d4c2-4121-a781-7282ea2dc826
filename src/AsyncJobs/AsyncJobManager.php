<?php

namespace App\AsyncJobs;

use Exception;
use PDO;

/**
 * Main Async Job Manager
 *
 * Orchestrates all async job processing. Extensible to handle multiple job types
 * including current screenshot capture and future video generation.
 */
class AsyncJobManager {
    private array $config;
    private JobDatabase $database;
    private JobLogger $logger;
    private PDO $db;
    private array $jobTypes;

    public function __construct(array $config) {
        $this->config = $config;
        $this->initializeDatabase();
        $this->database = new JobDatabase($this->db, $this->config);
        $this->logger = new JobLogger($this->db, $this->config);

        // Define available job types and their worker scripts
        $this->jobTypes = [
            'screenshot' => [
                'enabled' => $config['async_jobs']['screenshot']['enabled'] ?? false,
                'worker_script' => __DIR__ . '/../../scripts/screenshot_worker.php',
                'description' => 'Camera screenshot capture'
            ],
            'video' => [
                'enabled' => $config['async_jobs']['video']['enabled'] ?? false,
                'worker_script' => __DIR__ . '/../../scripts/video_worker.php',
                'description' => 'Video timelapse generation'
            ]
        ];

        $this->logger->info("AsyncJobManager initialized", null, [
            'enabled_job_types' => array_keys(array_filter($this->jobTypes, fn($type) => $type['enabled']))
        ]);
    }

    /**
     * Initialize database connection
     */
    private function initializeDatabase(): void {
        $dbConfig = $this->config['database'];

        $dsn = sprintf(
            "mysql:host=%s;dbname=%s;charset=%s",
            $dbConfig['host'],
            $dbConfig['dbname'],
            $dbConfig['charset']
        );

        $this->db = new PDO(
            $dsn,
            $dbConfig['username'],
            $dbConfig['password'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]
        );
    }

    /**
     * Create screenshot jobs for all cameras
     */
    public function createScreenshotJobs(array $cameraIds = null, int $priority = 0): array {
        if (!$this->jobTypes['screenshot']['enabled']) {
            throw new Exception("Screenshot jobs are disabled in configuration");
        }

        $cameras = $cameraIds ? $this->getCamerasByIds($cameraIds) : $this->database->getCamerasForScreenshots();
        $createdJobs = [];

        foreach ($cameras as $camera) {
            // Check if camera already has an active screenshot job
            if ($this->database->hasActiveJob('screenshot', $camera['id'])) {
                $this->logger->info("Skipping camera - active job exists", null, [
                    'camera_id' => $camera['id'],
                    'camera_name' => $camera['name']
                ]);
                continue;
            }

            $jobId = $this->database->createJob('screenshot', $camera['id'], [], $priority);
            $createdJobs[] = [
                'job_id' => $jobId,
                'camera_id' => $camera['id'],
                'camera_name' => $camera['name']
            ];

            $this->logger->info("Created screenshot job", $jobId, [
                'camera_id' => $camera['id'],
                'camera_name' => $camera['name'],
                'priority' => $priority
            ]);
        }

        return $createdJobs;
    }

    /**
     * Create video generation jobs for cameras
     */
    public function createVideoJobs(array $cameraIds = null, array $config = [], int $priority = 0): array {
        if (!$this->jobTypes['video']['enabled']) {
            throw new Exception("Video jobs are disabled in configuration");
        }

        $cameras = $cameraIds ? $this->getCamerasByIds($cameraIds) : $this->database->getCamerasForScreenshots();
        $createdJobs = [];

        foreach ($cameras as $camera) {
            // Check if camera already has an active video job
            if ($this->database->hasActiveJob('video', $camera['id'])) {
                $this->logger->info("Skipping camera - active video job exists", null, [
                    'camera_id' => $camera['id'],
                    'camera_name' => $camera['name']
                ]);
                continue;
            }

            $jobId = $this->database->createJob('video', $camera['id'], $config, $priority);
            $createdJobs[] = [
                'job_id' => $jobId,
                'camera_id' => $camera['id'],
                'camera_name' => $camera['name']
            ];

            $this->logger->info("Created video job", $jobId, [
                'camera_id' => $camera['id'],
                'camera_name' => $camera['name'],
                'priority' => $priority,
                'config' => $config
            ]);
        }

        return $createdJobs;
    }

    /**
     * Process all pending jobs of a specific type
     */
    public function processJobs(string $jobType): array {
        if (!isset($this->jobTypes[$jobType])) {
            throw new Exception("Unknown job type: {$jobType}");
        }

        if (!$this->jobTypes[$jobType]['enabled']) {
            throw new Exception("Job type '{$jobType}' is disabled in configuration");
        }

        $workerScript = $this->jobTypes[$jobType]['worker_script'];
        if (!file_exists($workerScript)) {
            throw new Exception("Worker script not found: {$workerScript}");
        }

        $this->logger->info("Starting job processing", null, [
            'job_type' => $jobType,
            'worker_script' => basename($workerScript)
        ]);

        // Create process pool for this job type
        $pool = new JobProcessPool(
            $this->config,
            $this->database,
            $this->logger,
            $jobType,
            $workerScript
        );

        // Process all pending jobs
        $results = $pool->processJobs();

        // Update statistics
        $this->updateJobStatistics($jobType, $results);

        $this->logger->info("Job processing completed", null, [
            'job_type' => $jobType,
            'processed_jobs' => count($results),
            'successful_jobs' => count(array_filter($results, fn($r) => $r['success'])),
            'failed_jobs' => count(array_filter($results, fn($r) => !$r['success']))
        ]);

        return $results;
    }

    /**
     * Process all enabled job types
     */
    public function processAllJobs(): array {
        $allResults = [];

        foreach ($this->jobTypes as $jobType => $config) {
            if ($config['enabled']) {
                try {
                    $results = $this->processJobs($jobType);
                    $allResults[$jobType] = $results;
                } catch (Exception $e) {
                    $this->logger->error("Failed to process {$jobType} jobs: " . $e->getMessage());
                    $allResults[$jobType] = ['error' => $e->getMessage()];
                }
            }
        }

        return $allResults;
    }

    /**
     * Get job queue status
     */
    public function getQueueStatus(): array {
        $stats = $this->database->getQueueStats();
        $organized = [];

        foreach ($stats as $stat) {
            $jobType = $stat['job_type'];
            $status = $stat['status'];
            $count = $stat['count'];

            if (!isset($organized[$jobType])) {
                $organized[$jobType] = [
                    'enabled' => $this->jobTypes[$jobType]['enabled'] ?? false,
                    'description' => $this->jobTypes[$jobType]['description'] ?? '',
                    'pending' => 0,
                    'running' => 0,
                    'completed' => 0,
                    'failed' => 0,
                    'timeout' => 0
                ];
            }

            $organized[$jobType][$status] = $count;
        }

        return $organized;
    }

    /**
     * Clean up old jobs and logs
     */
    public function cleanup(): array {
        $this->logger->info("Starting cleanup process");

        $cleanedJobs = $this->database->cleanupOldJobs();
        $cleanedLogs = $this->logger->cleanupOldLogs();

        // Handle timed out jobs
        $timedOutJobs = $this->database->getTimedOutJobs();
        foreach ($timedOutJobs as $job) {
            $this->database->markJobAsTimeout($job['id']);
            $this->logger->warning("Marked job as timed out", $job['id'], [
                'job_type' => $job['job_type'],
                'camera_id' => $job['camera_id'],
                'started_at' => $job['started_at']
            ]);
        }

        $results = [
            'cleaned_jobs' => $cleanedJobs,
            'cleaned_logs' => $cleanedLogs,
            'timed_out_jobs' => count($timedOutJobs)
        ];

        $this->logger->info("Cleanup completed", null, $results);
        return $results;
    }

    /**
     * Get cameras by IDs
     */
    private function getCamerasByIds(array $cameraIds): array {
        $placeholders = str_repeat('?,', count($cameraIds) - 1) . '?';
        $stmt = $this->db->prepare("
            SELECT id, name, hls_url 
            FROM cameras 
            WHERE id IN ({$placeholders}) 
            AND hls_url IS NOT NULL
        ");
        $stmt->execute($cameraIds);
        return $stmt->fetchAll();
    }

    /**
     * Update job statistics
     */
    private function updateJobStatistics(string $jobType, array $results): void {
        if (empty($results)) {
            return;
        }

        $successful = array_filter($results, fn($r) => $r['success']);
        $failed = array_filter($results, fn($r) => !$r['success']);
        $executionTimes = array_column($results, 'execution_time');

        $stats = [
            'total_jobs' => count($results),
            'completed_jobs' => count($successful),
            'failed_jobs' => count($failed),
            'avg_execution_time' => !empty($executionTimes) ? array_sum($executionTimes) / count($executionTimes) : 0,
            'max_execution_time' => !empty($executionTimes) ? max($executionTimes) : 0,
            'total_execution_time' => !empty($executionTimes) ? array_sum($executionTimes) : 0
        ];

        $this->database->updateJobStats($jobType, $stats);
    }

    /**
     * Get configuration for debugging
     */
    public function getConfig(): array {
        return $this->config;
    }

    /**
     * Get available job types
     */
    public function getJobTypes(): array {
        return $this->jobTypes;
    }
}
