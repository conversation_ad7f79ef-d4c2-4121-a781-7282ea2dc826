<?php

namespace App\AsyncJobs;

use Exception;

/**
 * Centralized Configuration Management for Async Jobs
 * 
 * Handles configuration for all job types with validation and defaults.
 * Designed to be extensible for future job types like video generation.
 */
class JobConfig {
    private array $config;
    private array $defaults;

    public function __construct(array $config) {
        $this->config = $config;
        $this->initializeDefaults();
        $this->validateConfiguration();
    }

    /**
     * Initialize default configuration values
     */
    private function initializeDefaults(): void {
        $this->defaults = [
            'async_jobs' => [
                'max_workers' => 6,
                'worker_timeout' => 3600, // 1 hour
                'job_retry_limit' => 3,
                'cleanup_completed_jobs_after_days' => 7,
                'log_level' => 'info',
                'log_retention_days' => 30,
                'heartbeat_interval' => 30,
                'worker_check_interval' => 5,
                
                'screenshot' => [
                    'enabled' => true,
                    'max_workers' => 4,
                    'worker_timeout' => 1800,
                    'output_dir' => __DIR__ . '/../../screenshots',
                    'ffmpeg_path' => '/usr/bin/ffmpeg',
                    'cjpeg_path' => '/usr/local/bin/cjpeg',
                    'command_template' => '%s -i "%s" -vframes 1 -vf "select=\'eq(pict_type,I)\'" -c:v mjpeg -q:v 5 -f mjpeg "%s.tmp" && %s -quality 75 -progressive -optimize -nojfif "%s.tmp" > "%s" && rm "%s.tmp"',
                    'image_variations' => [
                        'original' => ['enabled' => true],
                        'logo_overlay' => ['enabled' => true],
                        'thumbnail_720p' => ['enabled' => true, 'width' => 1280, 'height' => 720],
                        'thumbnail_low' => ['enabled' => true, 'width' => 720, 'height' => 405]
                    ],
                    'upload_to_backblaze' => false,
                ],
                
                'video' => [
                    'enabled' => false,
                    'max_workers' => 2,
                    'worker_timeout' => 7200,
                    'output_dir' => __DIR__ . '/../../videos',
                    'temp_dir' => __DIR__ . '/../../temp/video',
                    'ffmpeg_path' => '/usr/bin/ffmpeg',
                    'formats' => [
                        'timelapse_1080p' => [
                            'enabled' => true,
                            'width' => 1920,
                            'height' => 1080,
                            'fps' => 30,
                            'quality' => 'high',
                            'codec' => 'h264'
                        ],
                        'timelapse_720p' => [
                            'enabled' => true,
                            'width' => 1280,
                            'height' => 720,
                            'fps' => 30,
                            'quality' => 'medium',
                            'codec' => 'h264'
                        ]
                    ],
                    'source_timeframe' => [
                        'hours' => 24,
                        'min_images' => 10
                    ]
                ]
            ]
        ];
    }

    /**
     * Validate configuration and merge with defaults
     */
    private function validateConfiguration(): void {
        // Merge with defaults
        $this->config = array_replace_recursive($this->defaults, $this->config);
        
        // Validate required paths exist
        $this->validatePaths();
        
        // Validate numeric values
        $this->validateNumericValues();
    }

    /**
     * Validate that required paths exist and are accessible
     */
    private function validatePaths(): void {
        $pathsToCheck = [
            'screenshot.ffmpeg_path' => $this->get('async_jobs.screenshot.ffmpeg_path'),
            'video.ffmpeg_path' => $this->get('async_jobs.video.ffmpeg_path'),
        ];

        foreach ($pathsToCheck as $configKey => $path) {
            if (!empty($path) && !file_exists($path)) {
                error_log("Warning: Path not found for {$configKey}: {$path}");
            }
        }
    }

    /**
     * Validate numeric configuration values
     */
    private function validateNumericValues(): void {
        $numericChecks = [
            'async_jobs.max_workers' => ['min' => 1, 'max' => 20],
            'async_jobs.worker_timeout' => ['min' => 60, 'max' => 86400],
            'async_jobs.screenshot.max_workers' => ['min' => 1, 'max' => 10],
            'async_jobs.video.max_workers' => ['min' => 1, 'max' => 5],
        ];

        foreach ($numericChecks as $key => $limits) {
            $value = $this->get($key);
            if ($value < $limits['min'] || $value > $limits['max']) {
                throw new Exception("Configuration value '{$key}' ({$value}) is outside valid range [{$limits['min']}-{$limits['max']}]");
            }
        }
    }

    /**
     * Get configuration value using dot notation
     */
    public function get(string $key, $default = null) {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * Get all configuration for a specific job type
     */
    public function getJobConfig(string $jobType): array {
        return $this->get("async_jobs.{$jobType}", []);
    }

    /**
     * Check if a job type is enabled
     */
    public function isJobTypeEnabled(string $jobType): bool {
        return $this->get("async_jobs.{$jobType}.enabled", false);
    }

    /**
     * Get maximum workers for a job type
     */
    public function getMaxWorkers(string $jobType): int {
        return $this->get("async_jobs.{$jobType}.max_workers", $this->get('async_jobs.max_workers', 6));
    }

    /**
     * Get worker timeout for a job type
     */
    public function getWorkerTimeout(string $jobType): int {
        return $this->get("async_jobs.{$jobType}.worker_timeout", $this->get('async_jobs.worker_timeout', 3600));
    }

    /**
     * Get all configuration (for debugging)
     */
    public function getAll(): array {
        return $this->config;
    }

    /**
     * Get database configuration
     */
    public function getDatabaseConfig(): array {
        return $this->get('database', []);
    }

    /**
     * Get Backblaze configuration
     */
    public function getBackblazeConfig(): array {
        return $this->get('backblaze', []);
    }
}
