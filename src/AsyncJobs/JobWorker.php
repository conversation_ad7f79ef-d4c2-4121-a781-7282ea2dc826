<?php

namespace App\AsyncJobs;

use PDO;
use Exception;

/**
 * Base Worker Class for Async Jobs
 *
 * Abstract base class that provides common functionality for all job workers.
 * Specific job types (screenshot, video) extend this class.
 */
abstract class JobWorker {
    protected array $config;
    protected JobDatabase $database;
    protected JobLogger $logger;
    protected array $job;
    protected array $jobConfig;
    protected int $jobId;
    protected string $jobType;
    protected int $cameraId;
    protected PDO $db;

    public function __construct(array $jobData) {
        $this->job = $jobData['job'];
        $this->jobId = $this->job['id'];
        $this->jobType = $this->job['job_type'];
        $this->cameraId = $this->job['camera_id'];

        // Initialize configuration
        $this->config = $jobData['config'];
        $this->jobConfig = $jobData['job_type_config'] ?? [];

        // Initialize database connection
        $this->initializeDatabase();

        // Initialize database operations and logger
        $this->database = new JobDatabase($this->db, $this->config);
        $this->logger = new JobLogger($this->db, $this->config);

        $this->logger->logJobStart($this->jobId, $this->jobType, $this->cameraId, $this->jobConfig);
    }

    /**
     * Initialize database connection
     */
    private function initializeDatabase(): void {
        $dbConfig = $this->config['database'];

        $dsn = sprintf(
            "mysql:host=%s;dbname=%s;charset=%s",
            $dbConfig['host'],
            $dbConfig['dbname'],
            $dbConfig['charset']
        );

        $this->db = new PDO(
            $dsn,
            $dbConfig['username'],
            $dbConfig['password'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]
        );
    }

    /**
     * Main execution method - must be implemented by subclasses
     */
    abstract public function execute(): array;

    /**
     * Run the worker and handle result/error reporting
     */
    public function run(): void {
        try {
            $this->logger->info("Worker starting execution", $this->jobId, [
                'worker_pid' => getmypid(),
                'job_type' => $this->jobType,
                'camera_id' => $this->cameraId
            ]);

            $result = $this->execute();
            
            $this->logger->logJobComplete($this->jobId, $this->jobType, $this->cameraId, $result);
            
            // Output structured result for the process pool to read
            $this->outputResult(true, null, $result);
            
        } catch (Exception $e) {
            $errorMessage = $e->getMessage();
            $this->logger->logJobFailure($this->jobId, $this->jobType, $this->cameraId, $errorMessage, [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Output structured error for the process pool to read
            $this->outputResult(false, $errorMessage, null);
        }
    }

    /**
     * Output structured result for process pool
     */
    protected function outputResult(bool $success, ?string $error, ?array $data): void {
        $result = [
            'success' => $success,
            'error' => $error,
            'data' => $data
        ];
        
        echo "WORKER_RESULT:" . json_encode($result, JSON_UNESCAPED_SLASHES) . "\n";
    }

    /**
     * Get camera information from database
     */
    protected function getCameraInfo(): array {
        $stmt = $this->db->prepare("SELECT * FROM cameras WHERE id = ?");
        $stmt->execute([$this->cameraId]);
        
        $camera = $stmt->fetch();
        if (!$camera) {
            throw new Exception("Camera not found: {$this->cameraId}");
        }
        
        return $camera;
    }

    /**
     * Execute shell command with logging
     */
    protected function executeCommand(string $command, string $description = null): array {
        $description = $description ?: "Executing command";
        
        $this->logger->debug("{$description}: {$command}", $this->jobId);
        
        $startTime = microtime(true);
        exec($command . " 2>&1", $output, $returnCode);
        $executionTime = microtime(true) - $startTime;
        
        $this->logger->debug("Command completed", $this->jobId, [
            'return_code' => $returnCode,
            'execution_time' => round($executionTime, 2),
            'output_lines' => count($output)
        ]);
        
        if ($returnCode !== 0) {
            $this->logger->error("Command failed", $this->jobId, [
                'command' => $command,
                'return_code' => $returnCode,
                'output' => implode("\n", $output)
            ]);
        }
        
        return [
            'success' => $returnCode === 0,
            'return_code' => $returnCode,
            'output' => $output,
            'execution_time' => $executionTime
        ];
    }

    /**
     * Create directory if it doesn't exist
     */
    protected function ensureDirectoryExists(string $directory): void {
        if (!file_exists($directory)) {
            if (!mkdir($directory, 0755, true)) {
                throw new Exception("Failed to create directory: {$directory}");
            }
            $this->logger->debug("Created directory: {$directory}", $this->jobId);
        }
    }

    /**
     * Generate unique filename with timestamp
     */
    protected function generateFilename(string $prefix, string $extension, int $timestamp = null): string {
        $timestamp = $timestamp ?: time();
        return sprintf("%s_%d_%d.%s", $prefix, $this->cameraId, $timestamp, $extension);
    }

    /**
     * Clean up temporary files
     */
    protected function cleanupFiles(array $files): void {
        foreach ($files as $file) {
            if (file_exists($file)) {
                unlink($file);
                $this->logger->debug("Cleaned up file: " . basename($file), $this->jobId);
            }
        }
    }

    /**
     * Validate required configuration
     */
    protected function validateConfig(array $requiredKeys): void {
        foreach ($requiredKeys as $key) {
            $value = $this->getConfigValue($key);
            if (empty($value)) {
                throw new Exception("Required configuration missing: {$key}");
            }
        }
    }

    /**
     * Get configuration value using dot notation
     */
    protected function getConfigValue(string $key, $default = null) {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * Get job-specific configuration value
     */
    protected function getJobConfig(string $key, $default = null) {
        return $this->jobConfig[$key] ?? $default;
    }

    /**
     * Log progress update
     */
    protected function logProgress(string $message, array $context = []): void {
        $this->logger->info($message, $this->jobId, array_merge([
            'progress' => true,
            'worker_pid' => getmypid()
        ], $context));
    }

    /**
     * Check if job should be cancelled (for long-running operations)
     */
    protected function shouldCancel(): bool {
        // Refresh job status from database
        $job = $this->database->getJob($this->jobId);
        return $job && in_array($job['status'], ['cancelled', 'timeout']);
    }

    /**
     * Update job progress in database (for long-running jobs)
     */
    protected function updateProgress(array $progressData): void {
        $currentResult = $this->job['result_json'] ? json_decode($this->job['result_json'], true) : [];
        $currentResult['progress'] = array_merge($currentResult['progress'] ?? [], $progressData);
        
        $stmt = $this->db->prepare("UPDATE async_jobs SET result_json = ? WHERE id = ?");
        $stmt->execute([json_encode($currentResult, JSON_UNESCAPED_SLASHES), $this->jobId]);
    }
}
