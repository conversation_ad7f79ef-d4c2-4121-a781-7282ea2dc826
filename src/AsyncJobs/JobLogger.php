<?php

namespace App\AsyncJobs;

use PDO;
use Exception;

/**
 * Multi-Type Job Logging System
 *
 * Provides centralized logging for all async job types with database persistence,
 * file logging, and context-aware message formatting.
 */
class JobLogger {
    private PDO $db;
    private JobConfig $config;
    private string $logLevel;
    private array $logLevels = [
        'debug' => 0,
        'info' => 1,
        'warning' => 2,
        'error' => 3,
        'critical' => 4
    ];

    public function __construct(PDO $db, JobConfig $config) {
        $this->db = $db;
        $this->config = $config;
        $this->logLevel = $config->get('async_jobs.log_level', 'info');
    }

    /**
     * Log a debug message
     */
    public function debug(string $message, ?int $jobId = null, array $context = []): void {
        $this->log('debug', $message, $jobId, $context);
    }

    /**
     * Log an info message
     */
    public function info(string $message, ?int $jobId = null, array $context = []): void {
        $this->log('info', $message, $jobId, $context);
    }

    /**
     * Log a warning message
     */
    public function warning(string $message, ?int $jobId = null, array $context = []): void {
        $this->log('warning', $message, $jobId, $context);
    }

    /**
     * Log an error message
     */
    public function error(string $message, ?int $jobId = null, array $context = []): void {
        $this->log('error', $message, $jobId, $context);
    }

    /**
     * Log a critical message
     */
    public function critical(string $message, ?int $jobId = null, array $context = []): void {
        $this->log('critical', $message, $jobId, $context);
    }

    /**
     * Main logging method
     */
    public function log(string $level, string $message, ?int $jobId = null, array $context = []): void {
        // Check if we should log this level
        if (!$this->shouldLog($level)) {
            return;
        }

        $formattedMessage = $this->formatMessage($message, $context);

        // Log to database if job ID is provided
        if ($jobId !== null) {
            $this->logToDatabase($level, $formattedMessage, $jobId, $context);
        }

        // Always log to PHP error log
        $this->logToFile($level, $formattedMessage, $jobId, $context);
    }

    /**
     * Check if we should log this level based on configuration
     */
    private function shouldLog(string $level): bool {
        $currentLevelValue = $this->logLevels[$this->logLevel] ?? 1;
        $messageLevelValue = $this->logLevels[$level] ?? 1;

        return $messageLevelValue >= $currentLevelValue;
    }

    /**
     * Format log message with context
     */
    private function formatMessage(string $message, array $context): string {
        if (empty($context)) {
            return $message;
        }

        // Replace placeholders in message
        foreach ($context as $key => $value) {
            $placeholder = '{' . $key . '}';
            if (strpos($message, $placeholder) !== false) {
                $message = str_replace($placeholder, $this->contextValueToString($value), $message);
            }
        }

        return $message;
    }

    /**
     * Convert context value to string representation
     */
    private function contextValueToString($value): string {
        if (is_string($value) || is_numeric($value)) {
            return (string) $value;
        }

        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }

        if (is_array($value) || is_object($value)) {
            return json_encode($value, JSON_UNESCAPED_SLASHES);
        }

        return (string) $value;
    }

    /**
     * Log to database
     */
    private function logToDatabase(string $level, string $message, int $jobId, array $context): void {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO async_job_logs (job_id, log_level, message, context_json)
                VALUES (?, ?, ?, ?)
            ");

            $contextJson = !empty($context) ? json_encode($context, JSON_UNESCAPED_SLASHES) : null;

            $stmt->execute([$jobId, $level, $message, $contextJson]);
        } catch (Exception $e) {
            // If database logging fails, log to file instead
            error_log("Failed to log to database: " . $e->getMessage());
            error_log("Original log message: [{$level}] {$message}");
        }
    }

    /**
     * Log to PHP error log with structured format
     */
    private function logToFile(string $level, string $message, ?int $jobId, array $context): void {
        $timestamp = date('Y-m-d H:i:s');
        $pid = getmypid();
        $jobIdStr = $jobId ? " [Job:{$jobId}]" : "";

        $logMessage = "[{$timestamp}] [PID:{$pid}]{$jobIdStr} [" . strtoupper($level) . "] {$message}";

        // Add context if present and not already in message
        if (!empty($context)) {
            $contextStr = json_encode($context, JSON_UNESCAPED_SLASHES);
            $logMessage .= " Context: {$contextStr}";
        }

        error_log($logMessage);
    }

namespace App\AsyncJobs;

use PDO;
use Exception;

/**
 * Multi-Type Job Logging System
 * 
 * Provides centralized logging for all async job types with database persistence,
 * file logging, and context-aware message formatting.
 */
class JobLogger {
    private PDO $db;
    private JobConfig $config;
    private string $logLevel;
    private array $logLevels = [
        'debug' => 0,
        'info' => 1,
        'warning' => 2,
        'error' => 3,
        'critical' => 4
    ];

    public function __construct(PDO $db, JobConfig $config) {
        $this->db = $db;
        $this->config = $config;
        $this->logLevel = $config->get('async_jobs.log_level', 'info');
    }

    /**
     * Log a debug message
     */
    public function debug(string $message, ?int $jobId = null, array $context = []): void {
        $this->log('debug', $message, $jobId, $context);
    }

    /**
     * Log an info message
     */
    public function info(string $message, ?int $jobId = null, array $context = []): void {
        $this->log('info', $message, $jobId, $context);
    }

    /**
     * Log a warning message
     */
    public function warning(string $message, ?int $jobId = null, array $context = []): void {
        $this->log('warning', $message, $jobId, $context);
    }

    /**
     * Log an error message
     */
    public function error(string $message, ?int $jobId = null, array $context = []): void {
        $this->log('error', $message, $jobId, $context);
    }

    /**
     * Log a critical message
     */
    public function critical(string $message, ?int $jobId = null, array $context = []): void {
        $this->log('critical', $message, $jobId, $context);
    }

    /**
     * Main logging method
     */
    public function log(string $level, string $message, ?int $jobId = null, array $context = []): void {
        // Check if we should log this level
        if (!$this->shouldLog($level)) {
            return;
        }

        $formattedMessage = $this->formatMessage($message, $context);
        
        // Log to database if job ID is provided
        if ($jobId !== null) {
            $this->logToDatabase($level, $formattedMessage, $jobId, $context);
        }
        
        // Always log to PHP error log
        $this->logToFile($level, $formattedMessage, $jobId, $context);
    }

    /**
     * Check if we should log this level based on configuration
     */
    private function shouldLog(string $level): bool {
        $currentLevelValue = $this->logLevels[$this->logLevel] ?? 1;
        $messageLevelValue = $this->logLevels[$level] ?? 1;
        
        return $messageLevelValue >= $currentLevelValue;
    }

    /**
     * Format log message with context
     */
    private function formatMessage(string $message, array $context): string {
        if (empty($context)) {
            return $message;
        }

        // Replace placeholders in message
        foreach ($context as $key => $value) {
            $placeholder = '{' . $key . '}';
            if (strpos($message, $placeholder) !== false) {
                $message = str_replace($placeholder, $this->contextValueToString($value), $message);
            }
        }

        return $message;
    }

    /**
     * Convert context value to string representation
     */
    private function contextValueToString($value): string {
        if (is_string($value) || is_numeric($value)) {
            return (string) $value;
        }
        
        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }
        
        if (is_array($value) || is_object($value)) {
            return json_encode($value, JSON_UNESCAPED_SLASHES);
        }
        
        return (string) $value;
    }

    /**
     * Log to database
     */
    private function logToDatabase(string $level, string $message, int $jobId, array $context): void {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO async_job_logs (job_id, log_level, message, context_json) 
                VALUES (?, ?, ?, ?)
            ");
            
            $contextJson = !empty($context) ? json_encode($context, JSON_UNESCAPED_SLASHES) : null;
            
            $stmt->execute([$jobId, $level, $message, $contextJson]);
        } catch (Exception $e) {
            // If database logging fails, log to file instead
            error_log("Failed to log to database: " . $e->getMessage());
            error_log("Original log message: [{$level}] {$message}");
        }
    }

    /**
     * Log to PHP error log with structured format
     */
    private function logToFile(string $level, string $message, ?int $jobId, array $context): void {
        $timestamp = date('Y-m-d H:i:s');
        $pid = getmypid();
        $jobIdStr = $jobId ? " [Job:{$jobId}]" : "";
        
        $logMessage = "[{$timestamp}] [PID:{$pid}]{$jobIdStr} [" . strtoupper($level) . "] {$message}";
        
        // Add context if present and not already in message
        if (!empty($context)) {
            $contextStr = json_encode($context, JSON_UNESCAPED_SLASHES);
            $logMessage .= " Context: {$contextStr}";
        }
        
        error_log($logMessage);
    }

    /**
     * Log job start
     */
    public function logJobStart(int $jobId, string $jobType, int $cameraId, array $config = []): void {
        $this->info("Job started", $jobId, [
            'job_type' => $jobType,
            'camera_id' => $cameraId,
            'worker_pid' => getmypid(),
            'config' => $config
        ]);
    }

    /**
     * Log job completion
     */
    public function logJobComplete(int $jobId, string $jobType, int $cameraId, array $result = []): void {
        $this->info("Job completed successfully", $jobId, [
            'job_type' => $jobType,
            'camera_id' => $cameraId,
            'worker_pid' => getmypid(),
            'result' => $result
        ]);
    }

    /**
     * Log job failure
     */
    public function logJobFailure(int $jobId, string $jobType, int $cameraId, string $error, array $context = []): void {
        $this->error("Job failed", $jobId, array_merge([
            'job_type' => $jobType,
            'camera_id' => $cameraId,
            'worker_pid' => getmypid(),
            'error' => $error
        ], $context));
    }

    /**
     * Get recent logs for a job
     */
    public function getJobLogs(int $jobId, int $limit = 100): array {
        $stmt = $this->db->prepare("
            SELECT log_level, message, context_json, created_at 
            FROM async_job_logs 
            WHERE job_id = ? 
            ORDER BY created_at DESC 
            LIMIT ?
        ");
        
        $stmt->execute([$jobId, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Clean up old logs based on retention policy
     */
    public function cleanupOldLogs(): int {
        $retentionDays = $this->config->get('async_jobs.log_retention_days', 30);
        
        $stmt = $this->db->prepare("
            DELETE FROM async_job_logs 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        
        $stmt->execute([$retentionDays]);
        return $stmt->rowCount();
    }

    /**
     * Get log statistics
     */
    public function getLogStats(string $jobType = null, int $days = 7): array {
        $whereClause = "";
        $params = [$days];
        
        if ($jobType) {
            $whereClause = "AND aj.job_type = ?";
            $params[] = $jobType;
        }
        
        $stmt = $this->db->prepare("
            SELECT 
                ajl.log_level,
                COUNT(*) as count,
                DATE(ajl.created_at) as log_date
            FROM async_job_logs ajl
            JOIN async_jobs aj ON ajl.job_id = aj.id
            WHERE ajl.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            {$whereClause}
            GROUP BY ajl.log_level, DATE(ajl.created_at)
            ORDER BY log_date DESC, ajl.log_level
        ");
        
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
